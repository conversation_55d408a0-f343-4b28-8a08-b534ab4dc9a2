var linkifyElement=function(e){"use strict";function t(e,t,n){var r=n[n.length-1];e.replaceChild(r,t);for(var i=n.length-2;i>=0;i--)e.insertBefore(n[i],r),r=n[i]}function n(e,t,n){for(var r=[],i=0;i<e.length;i++){var a=e[i];if("nl"===a.t&&t.nl2br)r.push(n.createElement("br"));else if(a.isLink&&t.check(a)){var o=t.resolve(a),l=o.formatted,s=o.formattedHref,d=o.tagName,f=o.className,u=o.target,c=o.rel,h=o.events,v=o.attributes,m=n.createElement(d);if(m.setAttribute("href",s),f&&m.setAttribute("class",f),u&&m.setAttribute("target",u),c&&m.setAttribute("rel",c),v)for(var g in v)m.setAttribute(g,v[g]);if(h)for(var p in h)m.addEventListener?m.addEventListener(p,h[p]):m.attachEvent&&m.attachEvent("on"+p,h[p]);m.appendChild(n.createTextNode(l)),r.push(m)}else r.push(n.createTextNode(a.toString()))}return r}function r(i,a,o){if(!i||1!==i.nodeType)throw new Error("Cannot linkify ".concat(i," - Invalid DOM Node type"));var l=a.ignoreTags;if("A"===i.tagName||l.indexOf(i.tagName)>=0)return i;for(var s=i.firstChild;s;){var d=void 0,f=void 0,u=void 0;switch(s.nodeType){case 1:r(s,a,o);break;case 3:if(d=s.nodeValue,0===(f=e.tokenize(d)).length||1===f.length&&"text"===f[0].t)break;t(i,s,u=n(f,a,o)),s=u[u.length-1]}s=s.nextSibling}return i}function i(t,n){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;try{i=i||document||window&&window.document||global&&global.document}catch(e){}if(!i)throw new Error("Cannot find document implementation. If you are in a non-browser environment like Node.js, pass the document implementation as the third argument to linkifyElement.");return r(t,n=new e.Options(n),i)}return i.helper=r,i.normalize=function(t){return new e.Options(t)},i}(linkify);
