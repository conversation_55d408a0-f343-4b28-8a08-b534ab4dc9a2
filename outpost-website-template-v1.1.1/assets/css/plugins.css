/* Plugin Marketplace Styles */

.plugin-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    overflow: hidden;
    position: relative;
    margin-bottom: 2rem;
}

.plugin-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.3);
}

.plugin-card.featured {
    border-color: #ffd700;
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
}

.plugin-card.featured:hover {
    box-shadow: 0 10px 30px rgba(255, 215, 0, 0.5);
}

.plugin-image-container {
    position: relative;
    overflow: hidden;
    height: 200px;
}

.plugin-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.plugin-card:hover .plugin-image {
    transform: scale(1.05);
}

.plugin-image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, transparent 0%, rgba(0, 0, 0, 0.7) 100%);
}

.plugin-container {
    padding: 1.5rem;
}

.plugin-name {
    display: block;
    font-size: 1.25rem;
    font-weight: bold;
    color: #fff;
    margin-bottom: 0.5rem;
}

.plugin-description {
    display: block;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 1rem;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
}

.plugin-tags {
    margin-bottom: 1rem;
}

.plugin-tag {
    display: inline-block;
    background: rgba(255, 255, 255, 0.2);
    color: #fff;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    margin-right: 0.5rem;
    margin-bottom: 0.25rem;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.plugin-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.plugin-price {
    font-size: 1.1rem;
    font-weight: bold;
    color: #4CAF50;
}

.plugin-category {
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.7);
    background: rgba(255, 255, 255, 0.1);
    padding: 0.25rem 0.5rem;
    border-radius: 8px;
}

.plugin-actions {
    display: flex;
    gap: 0.5rem;
}

.plugin-actions .btn {
    flex: 1;
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
}

.search-filter-bar {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 1.5rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.search-filter-bar .form-control,
.search-filter-bar .form-select {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: #fff;
    backdrop-filter: blur(5px);
}

.search-filter-bar .form-control::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.search-filter-bar .form-control:focus,
.search-filter-bar .form-select:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.5);
    box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
    color: #fff;
}

.search-filter-bar .form-select option {
    background: #333;
    color: #fff;
}

.featured-plugins {
    margin-bottom: 3rem;
}

.featured-plugins h2 {
    color: #fff;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.plugins h2 {
    color: #fff;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

/* Ribbon for featured plugins */
.ribbon {
    position: absolute;
    top: 15px;
    right: -30px;
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #333;
    padding: 5px 40px;
    font-size: 0.8rem;
    font-weight: bold;
    transform: rotate(45deg);
    z-index: 10;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

.ribbon span {
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Modal customizations */
.modal-content {
    background: rgba(33, 37, 41, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.modal-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.modal-footer {
    border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.modal-title {
    color: #fff;
}

.modal-body {
    color: #fff;
}

.modal-body h6 {
    color: #ffd700;
    margin-top: 1rem;
    margin-bottom: 0.5rem;
}

.modal-body .list-unstyled li {
    margin-bottom: 0.25rem;
}

.payment-options .btn {
    margin-bottom: 0.5rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .plugin-actions {
        flex-direction: column;
    }
    
    .plugin-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .search-filter-bar .d-flex {
        flex-direction: column;
        gap: 1rem;
    }
    
    .search-filter-bar .d-flex > * {
        width: 100% !important;
    }
}

/* Animation enhancements */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.plugin-card {
    animation: fadeInUp 0.6s ease-out;
}

.plugin-card:nth-child(2) {
    animation-delay: 0.1s;
}

.plugin-card:nth-child(3) {
    animation-delay: 0.2s;
}

.plugin-card:nth-child(4) {
    animation-delay: 0.3s;
}
