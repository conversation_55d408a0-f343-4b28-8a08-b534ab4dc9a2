# Ubuntu Server Deployment Guide

This guide will help you deploy your Rust Plugin Marketplace on an Ubuntu server with a custom domain.

## Prerequisites

- Ubuntu 20.04 LTS or newer
- Root or sudo access
- Domain name pointed to your server's IP address
- Basic knowledge of Linux command line

## Step 1: Server Preparation

### Update System
```bash
sudo apt update && sudo apt upgrade -y
```

### Install Required Packages
```bash
sudo apt install -y nginx php8.1-fpm php8.1-mysql php8.1-curl php8.1-gd php8.1-mbstring php8.1-xml php8.1-zip php8.1-json php8.1-opcache mysql-server certbot python3-certbot-nginx git composer curl wget unzip
```

### Configure PHP
```bash
sudo nano /etc/php/8.1/fpm/php.ini
```

Update these settings:
```ini
upload_max_filesize = 50M
post_max_size = 50M
max_execution_time = 300
memory_limit = 256M
```

Restart PHP-FPM:
```bash
sudo systemctl restart php8.1-fpm
```

## Step 2: Database Setup

### Secure MySQL Installation
```bash
sudo mysql_secure_installation
```

### Create Database and User
```bash
sudo mysql -u root -p
```

```sql
CREATE DATABASE plugin_marketplace;
CREATE USER 'marketplace_user'@'localhost' IDENTIFIED BY 'your_secure_password';
GRANT ALL PRIVILEGES ON plugin_marketplace.* TO 'marketplace_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### Create Database Tables
```sql
USE plugin_marketplace;

-- Users table
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    steam_id VARCHAR(20) UNIQUE NOT NULL,
    username VARCHAR(100) NOT NULL,
    email VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Orders table
CREATE TABLE orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_steam_id VARCHAR(20) NOT NULL,
    plugin_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    payment_method VARCHAR(20) NOT NULL,
    transaction_id VARCHAR(100) NOT NULL,
    status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_steam_id) REFERENCES users(steam_id)
);

-- Licenses table
CREATE TABLE licenses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT NOT NULL,
    license_key VARCHAR(20) UNIQUE NOT NULL,
    plugin_id INT NOT NULL,
    user_steam_id VARCHAR(20) NOT NULL,
    status ENUM('active', 'expired', 'revoked') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NULL,
    FOREIGN KEY (order_id) REFERENCES orders(id),
    FOREIGN KEY (user_steam_id) REFERENCES users(steam_id)
);

-- Downloads table
CREATE TABLE downloads (
    id INT AUTO_INCREMENT PRIMARY KEY,
    license_id INT NOT NULL,
    download_token VARCHAR(64) UNIQUE NOT NULL,
    download_count INT DEFAULT 0,
    max_downloads INT DEFAULT 5,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (license_id) REFERENCES licenses(id)
);

-- Download logs table
CREATE TABLE download_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    license_id INT NOT NULL,
    user_steam_id VARCHAR(20) NOT NULL,
    plugin_id INT NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT,
    downloaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (license_id) REFERENCES licenses(id)
);
```

## Step 3: Web Server Configuration

### Create Web Directory
```bash
sudo mkdir -p /var/www/your-domain.com
sudo chown -R www-data:www-data /var/www/your-domain.com
sudo chmod -R 755 /var/www/your-domain.com
```

### Upload Your Files
```bash
# If using git
cd /var/www/your-domain.com
sudo git clone https://github.com/yourusername/your-repo.git .

# Or upload files via SCP/SFTP to /var/www/your-domain.com
```

### Set Proper Permissions
```bash
sudo chown -R www-data:www-data /var/www/your-domain.com
sudo chmod -R 755 /var/www/your-domain.com
sudo chmod -R 777 /var/www/your-domain.com/logs
sudo chmod -R 777 /var/www/your-domain.com/downloads
```

### Create Nginx Configuration
```bash
sudo nano /etc/nginx/sites-available/your-domain.com
```

```nginx
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    root /var/www/your-domain.com;
    index index.php index.html index.htm;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        include snippets/fastcgi-php.conf;
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # Protect sensitive files
    location ~ /\. {
        deny all;
    }

    location ~ /(config|logs|admin)/ {
        deny all;
    }

    # Cache static files
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|woff|woff2|ttf|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Limit file upload size
    client_max_body_size 50M;
}
```

### Enable Site
```bash
sudo ln -s /etc/nginx/sites-available/your-domain.com /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## Step 4: SSL Certificate Setup

### Install SSL Certificate
```bash
sudo certbot --nginx -d your-domain.com -d www.your-domain.com
```

### Auto-renewal Setup
```bash
sudo crontab -e
```

Add this line:
```bash
0 12 * * * /usr/bin/certbot renew --quiet
```

## Step 5: Application Configuration

### Configure Database Connection
Create `/var/www/your-domain.com/database.php`:
```php
<?php
$db_config = [
    'host' => 'localhost',
    'database' => 'plugin_marketplace',
    'username' => 'marketplace_user',
    'password' => 'your_secure_password',
    'charset' => 'utf8mb4'
];

try {
    $pdo = new PDO(
        "mysql:host={$db_config['host']};dbname={$db_config['database']};charset={$db_config['charset']}",
        $db_config['username'],
        $db_config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ]
    );
} catch (PDOException $e) {
    die('Database connection failed: ' . $e->getMessage());
}
?>
```

### Update Configuration
Edit `/var/www/your-domain.com/config.php`:
```php
// Update these values
'title' => 'Your Server Name',
'description' => 'Your server description',
'domain' => 'https://your-domain.com',

// Payment configuration
'store' => [
    'return_url' => 'https://your-domain.com/payment-success.php',
    'cancel_url' => 'https://your-domain.com/payment-cancel.php',
    'success_url' => 'https://your-domain.com/payment-success.php',
    'crypto_callback_url' => 'https://your-domain.com/crypto-callback.php',
    'supportEmail' => '<EMAIL>',
    
    // Add your real payment credentials
    'paypal' => [
        'enabled' => true,
        'sandbox' => false, // Set to true for testing
        'client_id' => 'YOUR_PAYPAL_CLIENT_ID',
        'client_secret' => 'YOUR_PAYPAL_CLIENT_SECRET'
    ],
    
    'stripe' => [
        'enabled' => true,
        'publishable_key' => 'pk_live_YOUR_STRIPE_PUBLISHABLE_KEY',
        'secret_key' => 'sk_live_YOUR_STRIPE_SECRET_KEY'
    ]
]
```

## Step 6: Security Hardening

### Firewall Configuration
```bash
sudo ufw allow OpenSSH
sudo ufw allow 'Nginx Full'
sudo ufw enable
```

### Fail2Ban Setup
```bash
sudo apt install fail2ban
sudo nano /etc/fail2ban/jail.local
```

```ini
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 5

[nginx-http-auth]
enabled = true

[nginx-noscript]
enabled = true

[nginx-badbots]
enabled = true

[nginx-noproxy]
enabled = true
```

```bash
sudo systemctl enable fail2ban
sudo systemctl start fail2ban
```

### Regular Backups
Create backup script `/home/<USER>/backup.sh`:
```bash
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/home/<USER>/backups"
SITE_DIR="/var/www/your-domain.com"

mkdir -p $BACKUP_DIR

# Backup files
tar -czf $BACKUP_DIR/site_$DATE.tar.gz -C $SITE_DIR .

# Backup database
mysqldump -u marketplace_user -p'your_secure_password' plugin_marketplace > $BACKUP_DIR/db_$DATE.sql

# Keep only last 7 days of backups
find $BACKUP_DIR -name "site_*.tar.gz" -mtime +7 -delete
find $BACKUP_DIR -name "db_*.sql" -mtime +7 -delete
```

Make executable and add to cron:
```bash
chmod +x /home/<USER>/backup.sh
crontab -e
```

Add:
```bash
0 2 * * * /home/<USER>/backup.sh
```

## Step 7: Monitoring and Maintenance

### Log Monitoring
```bash
# Check Nginx logs
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log

# Check PHP logs
sudo tail -f /var/log/php8.1-fpm.log

# Check application logs
tail -f /var/www/your-domain.com/logs/downloads.log
tail -f /var/www/your-domain.com/logs/purchases.log
```

### Performance Monitoring
Install monitoring tools:
```bash
sudo apt install htop iotop nethogs
```

### Regular Updates
```bash
# System updates
sudo apt update && sudo apt upgrade -y

# Composer updates (if using)
cd /var/www/your-domain.com
sudo -u www-data composer update
```

## Step 8: Domain Configuration

### DNS Settings
Configure these DNS records with your domain provider:

```
A     @              YOUR_SERVER_IP
A     www            YOUR_SERVER_IP
CNAME mail           @
MX    @              mail.your-domain.com (priority 10)
```

### Email Setup (Optional)
For sending emails from your application:
```bash
sudo apt install postfix mailutils
```

Configure Postfix for your domain during installation.

## Troubleshooting

### Common Issues

1. **Permission Errors**
   ```bash
   sudo chown -R www-data:www-data /var/www/your-domain.com
   sudo chmod -R 755 /var/www/your-domain.com
   ```

2. **PHP Errors**
   ```bash
   sudo tail -f /var/log/php8.1-fpm.log
   sudo systemctl restart php8.1-fpm
   ```

3. **Nginx Errors**
   ```bash
   sudo nginx -t
   sudo systemctl restart nginx
   ```

4. **Database Connection Issues**
   ```bash
   mysql -u marketplace_user -p plugin_marketplace
   ```

### Performance Optimization

1. **Enable OPcache**
   ```bash
   sudo nano /etc/php/8.1/fpm/conf.d/10-opcache.ini
   ```
   
   ```ini
   opcache.enable=1
   opcache.memory_consumption=128
   opcache.max_accelerated_files=4000
   opcache.revalidate_freq=60
   ```

2. **Configure Nginx Caching**
   Add to your Nginx config:
   ```nginx
   location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
       expires 1y;
       add_header Cache-Control "public, immutable";
   }
   ```

## Support

If you encounter issues during deployment:
1. Check the logs mentioned in the monitoring section
2. Verify all configuration files are correct
3. Ensure all services are running: `sudo systemctl status nginx php8.1-fpm mysql`
4. Test your domain configuration with online tools

Remember to replace all placeholder values (your-domain.com, passwords, API keys) with your actual values before deployment.
