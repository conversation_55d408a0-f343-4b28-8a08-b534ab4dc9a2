<?php
session_start();

/**
 * license-manager.php
 * License Key Management System
 * @owner     Plugin Marketplace
 * @copyright 2024 Plugin Marketplace
 */

require_once __DIR__ . '/plugin-core.php';

// Check if user is logged in
if (!isset($_SESSION['steamid'])) {
    header('Location: index.php');
    exit;
}

$action = $_GET['action'] ?? 'view';
$licenseKey = $_GET['key'] ?? '';

include 'templates/head.php';
include 'templates/navigation.php';
?>

<div class="container">
    <main>
        <section class="license-manager py-5">
            <div class="row">
                <div class="col-md-12">
                    <h2><i class="fas fa-key"></i> License Manager</h2>
                    <p class="lead">Manage your plugin licenses and download history</p>
                </div>
            </div>

            <?php if ($action === 'view'): ?>
            <!-- License List View -->
            <div class="row">
                <div class="col-md-12">
                    <div class="card bg-dark text-white">
                        <div class="card-header">
                            <h4 class="mb-0">Your Plugin Licenses</h4>
                        </div>
                        <div class="card-body">
                            <?php
                            $userLicenses = getUserLicenses($_SESSION['steamid']);
                            if (empty($userLicenses)): ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                                    <h5>No licenses found</h5>
                                    <p class="text-muted">You haven't purchased any plugins yet.</p>
                                    <a href="index.php#plugins" class="btn btn-primary">Browse Plugins</a>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-dark table-striped">
                                        <thead>
                                            <tr>
                                                <th>Plugin</th>
                                                <th>License Key</th>
                                                <th>Purchase Date</th>
                                                <th>Downloads</th>
                                                <th>Status</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($userLicenses as $license): ?>
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <img src="assets/img/<?php echo $license['plugin_image']; ?>" alt="<?php echo htmlspecialchars($license['plugin_name']); ?>" class="me-2" style="width: 40px; height: 40px; object-fit: cover; border-radius: 4px;">
                                                        <div>
                                                            <strong><?php echo htmlspecialchars($license['plugin_name']); ?></strong>
                                                            <br><small class="text-muted">v<?php echo htmlspecialchars($license['plugin_version']); ?></small>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <code class="license-key"><?php echo htmlspecialchars($license['license_key']); ?></code>
                                                    <button class="btn btn-sm btn-outline-light ms-2" onclick="copyToClipboard('<?php echo htmlspecialchars($license['license_key']); ?>')">
                                                        <i class="fas fa-copy"></i>
                                                    </button>
                                                </td>
                                                <td><?php echo date('M j, Y', strtotime($license['purchase_date'])); ?></td>
                                                <td>
                                                    <span class="badge bg-info"><?php echo $license['download_count']; ?>/<?php echo $license['max_downloads']; ?></span>
                                                </td>
                                                <td>
                                                    <?php if ($license['status'] === 'active'): ?>
                                                        <span class="badge bg-success">Active</span>
                                                    <?php elseif ($license['status'] === 'expired'): ?>
                                                        <span class="badge bg-warning">Expired</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-danger">Revoked</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <?php if ($license['download_count'] < $license['max_downloads'] && $license['status'] === 'active'): ?>
                                                        <a href="download.php?token=<?php echo generateDownloadToken($license['plugin_id'], $_SESSION['steamid']); ?>&plugin=<?php echo $license['plugin_id']; ?>" class="btn btn-primary">
                                                            <i class="fas fa-download"></i> Download
                                                        </a>
                                                        <?php endif; ?>
                                                        <a href="?action=details&key=<?php echo urlencode($license['license_key']); ?>" class="btn btn-outline-light">
                                                            <i class="fas fa-info-circle"></i> Details
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <?php elseif ($action === 'details' && !empty($licenseKey)): ?>
            <!-- License Details View -->
            <?php
            $licenseDetails = getLicenseDetails($licenseKey, $_SESSION['steamid']);
            if (!$licenseDetails): ?>
                <div class="alert alert-danger">
                    <h5>License not found</h5>
                    <p>The requested license key was not found or doesn't belong to your account.</p>
                    <a href="?action=view" class="btn btn-primary">Back to License List</a>
                </div>
            <?php else: ?>
                <div class="row">
                    <div class="col-md-8">
                        <div class="card bg-dark text-white">
                            <div class="card-header">
                                <h4 class="mb-0">License Details</h4>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h5>Plugin Information</h5>
                                        <ul class="list-unstyled">
                                            <li><strong>Name:</strong> <?php echo htmlspecialchars($licenseDetails['plugin_name']); ?></li>
                                            <li><strong>Version:</strong> <?php echo htmlspecialchars($licenseDetails['plugin_version']); ?></li>
                                            <li><strong>Author:</strong> <?php echo htmlspecialchars($licenseDetails['plugin_author']); ?></li>
                                            <li><strong>Category:</strong> <?php echo htmlspecialchars($licenseDetails['plugin_category']); ?></li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <h5>License Information</h5>
                                        <ul class="list-unstyled">
                                            <li><strong>License Key:</strong> <code><?php echo htmlspecialchars($licenseDetails['license_key']); ?></code></li>
                                            <li><strong>Purchase Date:</strong> <?php echo date('M j, Y H:i', strtotime($licenseDetails['purchase_date'])); ?></li>
                                            <li><strong>Status:</strong> 
                                                <?php if ($licenseDetails['status'] === 'active'): ?>
                                                    <span class="badge bg-success">Active</span>
                                                <?php elseif ($licenseDetails['status'] === 'expired'): ?>
                                                    <span class="badge bg-warning">Expired</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">Revoked</span>
                                                <?php endif; ?>
                                            </li>
                                            <li><strong>Downloads:</strong> <?php echo $licenseDetails['download_count']; ?>/<?php echo $licenseDetails['max_downloads']; ?></li>
                                        </ul>
                                    </div>
                                </div>

                                <hr>

                                <h5>Download History</h5>
                                <?php
                                $downloadHistory = getDownloadHistory($licenseDetails['license_key']);
                                if (empty($downloadHistory)): ?>
                                    <p class="text-muted">No downloads yet.</p>
                                <?php else: ?>
                                    <div class="table-responsive">
                                        <table class="table table-sm table-dark">
                                            <thead>
                                                <tr>
                                                    <th>Date</th>
                                                    <th>IP Address</th>
                                                    <th>User Agent</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($downloadHistory as $download): ?>
                                                <tr>
                                                    <td><?php echo date('M j, Y H:i', strtotime($download['timestamp'])); ?></td>
                                                    <td><?php echo htmlspecialchars($download['ip_address']); ?></td>
                                                    <td><?php echo htmlspecialchars(substr($download['user_agent'], 0, 50)); ?>...</td>
                                                </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="card-footer">
                                <div class="d-flex justify-content-between">
                                    <a href="?action=view" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left"></i> Back to Licenses
                                    </a>
                                    <?php if ($licenseDetails['download_count'] < $licenseDetails['max_downloads'] && $licenseDetails['status'] === 'active'): ?>
                                    <a href="download.php?token=<?php echo generateDownloadToken($licenseDetails['plugin_id'], $_SESSION['steamid']); ?>&plugin=<?php echo $licenseDetails['plugin_id']; ?>" class="btn btn-primary">
                                        <i class="fas fa-download"></i> Download Plugin
                                    </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-dark text-white">
                            <div class="card-header">
                                <h5 class="mb-0">Support</h5>
                            </div>
                            <div class="card-body">
                                <p>Need help with this plugin?</p>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-envelope"></i> <a href="mailto:<?php echo $config['store']['supportEmail'] ?? '<EMAIL>'; ?>">Email Support</a></li>
                                    <li><i class="fas fa-file-alt"></i> <a href="#" onclick="alert('Documentation coming soon!')">Documentation</a></li>
                                    <li><i class="fas fa-bug"></i> <a href="#" onclick="alert('Bug reporting coming soon!')">Report Bug</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
            <?php endif; ?>
        </section>
    </main>
</div>

<?php include 'templates/footer.php'; ?>

<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success message
        const toast = document.createElement('div');
        toast.className = 'toast-notification';
        toast.textContent = 'License key copied to clipboard!';
        toast.style.cssText = 'position: fixed; top: 20px; right: 20px; background: #28a745; color: white; padding: 10px 20px; border-radius: 5px; z-index: 9999;';
        document.body.appendChild(toast);
        
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 3000);
    });
}
</script>

<style>
.license-key {
    background: rgba(255, 255, 255, 0.1);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-family: 'Courier New', monospace;
}

.table-dark th {
    border-color: rgba(255, 255, 255, 0.2);
}

.table-dark td {
    border-color: rgba(255, 255, 255, 0.1);
}

.card {
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}
</style>

<?php
/**
 * Get user licenses (demo data)
 */
function getUserLicenses($steamId) {
    // In a real implementation, this would query a database
    // For demo purposes, return sample data
    return [
        [
            'license_key' => 'ABCD-1234-EFGH-5678',
            'plugin_id' => 1,
            'plugin_name' => 'Advanced Teleportation',
            'plugin_version' => '2.1.0',
            'plugin_image' => 'plugin-teleport.jpg',
            'purchase_date' => '2024-06-20 14:30:00',
            'download_count' => 2,
            'max_downloads' => 5,
            'status' => 'active'
        ],
        [
            'license_key' => 'WXYZ-9876-MNOP-5432',
            'plugin_id' => 2,
            'plugin_name' => 'Enhanced PvP System',
            'plugin_version' => '1.5.2',
            'plugin_image' => 'plugin-pvp.webp',
            'purchase_date' => '2024-06-22 09:15:00',
            'download_count' => 1,
            'max_downloads' => 5,
            'status' => 'active'
        ]
    ];
}

/**
 * Get license details
 */
function getLicenseDetails($licenseKey, $steamId) {
    $licenses = getUserLicenses($steamId);
    foreach ($licenses as $license) {
        if ($license['license_key'] === $licenseKey) {
            // Add additional details
            $license['plugin_author'] = 'PluginDev';
            $license['plugin_category'] = 'Admin Tools';
            return $license;
        }
    }
    return null;
}

/**
 * Get download history for a license
 */
function getDownloadHistory($licenseKey) {
    // In a real implementation, this would query a database
    return [
        [
            'timestamp' => '2024-06-20 14:35:00',
            'ip_address' => '*************',
            'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        ],
        [
            'timestamp' => '2024-06-21 10:20:00',
            'ip_address' => '*************',
            'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        ]
    ];
}
?>
