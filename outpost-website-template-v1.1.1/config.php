<?php

	/**
	 * config.php
	 * @owner     illRalli <<EMAIL>>
	 * @copyright  2024 Promeus Group
	 */

return [

	/*
	|--------------------------------------------------------------------------
	| Website information
	|--------------------------------------------------------------------------
	|
	| The title and description is used in the header part (hero) of the website.
	| Include your server name in the title to improve SEO results. The welcome text
	| is put in front of the title
	|
	*/

	'welcome' => 'Welcome to',

	'title' => 'Promeus Rust',

	'description' => 'Your favourite server template, enjoy your time',

	/*
	|--------------------------------------------------------------------------
	| Logo and background image
	|--------------------------------------------------------------------------
	|
	| You can change your server logo here. Uplaod your logo into the img folder
	| and replace the file name here, or rename it to the logo.png default. If
	| logo is empty, it will display the title instead of the logo. Background
	| image should not be left empty. The logo needs to have a height of 75px,
	| while the width does not matter.
	|
	| IMPORTANT: If you do not have a logo with the same sizes (e.g. 75x75)
	| you need to manually change the width in the CSS code. Search for
	| .navbar-rust .navbar-logo-container in the style.css file and replace
	| the width with your logo's width.
	|
	*/

	'logo' => 'logo.webp',

	'backgroundImage' => 'background.webp',

    'shopImage' => 'vending.webp',

	/*
	|--------------------------------------------------------------------------
	| Plugins
	|--------------------------------------------------------------------------
	|
	| You can add a list of available Rust plugins here. Each plugin should have
	| a name, description, price, category, image, and download information.
	| Images should be uploaded to the assets/img folder. Categories help organize
	| plugins for easier browsing. Set 'featured' to true to highlight popular plugins.
	| The 'file' field should point to the plugin file in the downloads folder.
	|
	*/

	'plugins' => [
        [
            'id' => 1,
            'name' => 'Advanced Teleportation',
            'description' => 'A comprehensive teleportation system with homes, warps, and admin teleports. Perfect for enhancing player experience.',
            'price' => 15.99,
            'category' => 'Admin Tools',
            'image' => 'plugin-teleport.webp',
            'featured' => true,
            'file' => 'AdvancedTeleportation.cs',
            'version' => '2.1.0',
            'author' => 'PluginDev',
            'compatibility' => 'Oxide/uMod',
            'tags' => ['teleport', 'admin', 'utility'],
            'changelog' => 'Fixed teleport cooldowns, added new warp system',
            'requirements' => 'Oxide/uMod framework required',
            'date_added' => '2024-06-18'
        ],
        [
            'id' => 2,
            'name' => 'Enhanced PvP System',
            'description' => 'Advanced PvP mechanics with kill streaks, bounties, and leaderboards. Boost your server\'s competitive gameplay.',
            'price' => 24.99,
            'category' => 'PvP',
            'image' => 'plugin-pvp.webp',
            'featured' => true,
            'file' => 'EnhancedPvP.cs',
            'version' => '1.5.2',
            'author' => 'CombatCoder',
            'compatibility' => 'Oxide/uMod',
            'tags' => ['pvp', 'combat', 'leaderboard'],
            'changelog' => 'Added bounty system, improved kill tracking',
            'requirements' => 'Oxide/uMod framework required',
            'date_added' => '2024-06-22'
        ],
        [
            'id' => 3,
            'name' => 'Auto Resource Gatherer',
            'description' => 'Automated resource collection system for PvE servers. Configurable rates and resource types.',
            'price' => 12.99,
            'category' => 'PvE',
            'image' => 'plugin-gather.webp',
            'featured' => false,
            'file' => 'AutoGatherer.cs',
            'version' => '1.2.1',
            'author' => 'ResourceMaster',
            'compatibility' => 'Oxide/uMod',
            'tags' => ['pve', 'gathering', 'automation'],
            'changelog' => 'Optimized performance, added new resource types',
            'requirements' => 'Oxide/uMod framework required',
            'date_added' => '2024-06-15'
        ]
    ],

	/*
	|--------------------------------------------------------------------------
	| Plugin Categories
	|--------------------------------------------------------------------------
	|
	| Define the available plugin categories. These are used for filtering
	| and organizing plugins on the website.
	|
	*/

	'pluginCategories' => [
		'Admin Tools',
		'PvP',
		'PvE',
		'Economy',
		'Building',
		'Transportation',
		'Chat & Communication',
		'Events',
		'Utilities',
		'Fun & Games'
	],

	/*
	|--------------------------------------------------------------------------
	| Maps
	|--------------------------------------------------------------------------
	|
	| You can add a list of available Rust maps here. Each map should have
	| a name, description, size, type, image, and download information.
	| Images should be uploaded to the assets/img folder. Map files should
	| be uploaded to the downloads/maps folder.
	|
	*/

	'maps' => [
		[
			'id' => 1,
			'name' => 'Rust Island',
			'description' => 'A classic Rust island map with diverse biomes and strategic locations.',
			'size' => '4000x4000',
			'type' => 'Procedural',
			'image' => 'map-rust-island.jpg',
			'featured' => true,
			'file' => 'RustIsland.map',
			'version' => '1.0',
			'author' => 'Facepunch Studios',
			'seed' => '12345678',
			'tags' => ['island', 'classic', 'balanced'],
			'monuments' => ['Launch Site', 'Military Tunnels', 'Airfield', 'Water Treatment'],
			'date_added' => '2024-06-25',
			'download_count' => 0,
			'rating' => 4.5
		]
	],

	/*
	|--------------------------------------------------------------------------
	| Map Categories
	|--------------------------------------------------------------------------
	|
	| Define the available map categories for organization
	|
	*/

	'mapCategories' => [
		'Procedural',
		'Custom',
		'PvP',
		'PvE',
		'Roleplay',
		'Survival',
		'Creative',
		'Mini Games'
	],

    /*
	|--------------------------------------------------------------------------
	| Connect Button
	|--------------------------------------------------------------------------
    |
	| You have two different options to join the server. The first way with the connect button, there you can join
	| directly on the server. If the steam link is not available you can use the second method - the pop up - with
    | the instructions to join the server via the ingame console.
    |
    | Options: steam, popup
    |
	*/

    'connectButton' => 'popup',

	/*
	|--------------------------------------------------------------------------
	| Data API
	|--------------------------------------------------------------------------
	|
	| This website has 2 different ways of gaining information for your server. One is
	| the BattleMetrics API, and another one is SourceQuery. SourceQuery is directly connecting
	| to your server (and requires the sockets extension and open ports if you use it).
	|
	| By default we use the BattleMetrics API so the website can work on most webhostings. However,
	| if you want to support tags (as shown ingame in the serverlist, the blue information) you need to
	| use the SourceQuery option. BattleMetrics supports a maximum of 15 servers.
	|
	| Options: battlemetrics, sourcequery
	|
	*/

	'api' => 'battlemetrics',

	/*
	|--------------------------------------------------------------------------
	| Last wiped information
	|--------------------------------------------------------------------------
	|
	| If last wiped is enabled, servers do show a just wiped banner if they have
	| recently been wiped. Set the hours passed since wipe until the banner is
	| hidden again.
	|
	*/

	'lastWiped' => [

		'enabled' => 'yes',

		'text' => 'Just Wiped',

		'hoursPassed' => 24,

	],

	/*
	|--------------------------------------------------------------------------
	| Store settings
	|--------------------------------------------------------------------------
	|
	| Configure the plugin store settings. This controls the main store section
	| and payment processing options.
	|
	*/

	'store' => [

		'enabled' => 'yes',

		'heading' => 'Plugin Store',

		'navigation' => 'Plugins',

		'button' => 'Browse Plugins',

		'message' => 'Enhance your Rust server with premium plugins! Our collection includes: <ul><li>Admin Tools & Utilities</li><li>PvP & Combat Systems</li><li>Economy & Trading</li><li>Building & Construction</li><li>And much more!</li></ul>',

		'currency' => 'USD',

		'paymentMethods' => [
			'paypal' => true,
			'stripe' => true,
			'crypto' => false
		],

		'downloadExpiry' => 30, // Days until download link expires

		'return_url' => 'https://yourserver.com/payment-success.php',
		'cancel_url' => 'https://yourserver.com/payment-cancel.php',
		'success_url' => 'https://yourserver.com/payment-success.php',
		'crypto_callback_url' => 'https://yourserver.com/crypto-callback.php',

		// PayPal Configuration
		'paypal' => [
			'enabled' => true,
			'sandbox' => true, // Set to false for production
			'client_id' => 'YOUR_PAYPAL_CLIENT_ID',
			'client_secret' => 'YOUR_PAYPAL_CLIENT_SECRET'
		],

		// Stripe Configuration
		'stripe' => [
			'enabled' => true,
			'publishable_key' => 'pk_test_YOUR_STRIPE_PUBLISHABLE_KEY',
			'secret_key' => 'sk_test_YOUR_STRIPE_SECRET_KEY'
		],

		// Cryptocurrency Configuration
		'crypto' => [
			'enabled' => true,
			'sandbox' => true, // Set to false for production
			'api_token' => 'YOUR_COINGATE_API_TOKEN',
			'receive_currency' => 'BTC' // BTC, ETH, LTC, etc.
		]

	],

	/*
	|--------------------------------------------------------------------------
	| Discord settings
	|--------------------------------------------------------------------------
	|
	| If inviteLink is set, a button in the navigation will show up as a
	| Call-To-Action for users to join your discord server. It will disappear
	| if you remove the link. The link needs to start with the protocol (https://).
	|
	| To show your Discord player count, you need to include your discord server ID.
	| This is only required if you want to show the amount of people on your Discord server.
	| To get the server ID:
	|
	| 1. Go to your Server Settings. Click on Widget.
	| 2. Enable Server Widget at the top.
	| 3. Copy the server ID shown in the box below into serverId
	|
	*/

	'discord' => [

		'inviteLink' => 'https://discord.gg',

		'text' => 'Join Discord',

		'serverId' => '', // <-- Check the comment above for more information

	],

	/*
	|--------------------------------------------------------------------------
	| Social links
	|--------------------------------------------------------------------------
	|
	| Social links are only displayed if not empty. If entered, they will show
	| up with an icon for the specific platform. All links need to start with
	| their protocol (https://)
	|
	*/

	'socials' => [

		'enabled' => 'no',

		'links' => [
			'steam'     => '',
			'youtube'   => '',
			'twitch'    => '',
			'twitter'   => '',
			'instagram' => '',
			'facebook'  => '',
			'vk'        => '',
			'tiktok'	=> '',
		],

	],

	/*
	|--------------------------------------------------------------------------
	| Rules
	|--------------------------------------------------------------------------
	|
	| If enabled, it will show your server rules on the bottom of the page.
	| Feel free to add any new rules you feel applicable to your server, they
	| will automatically show up on the page. If you want a new line within your
	| text, you can use a <br> (HTML for new line) to start a new line. This will
	| display a new line on the website.
	|
	*/

	'rules' => [

		'enabled' => 'yes',

		'heading' => 'Rules',

		'navigation' => 'Rules',

		'rules' => [

            [
                'title' => 'Ban evasion',
                'text' => 'Do not do anything to bypass a ban on our server(s). Do not try to join with a different account if you were banned, sit it out. Any try to do so will result in additional bans.',
            ],
            [
                'title' => 'Bug abuse',
                'text' => 'Do not try to use any unfair advantages which were not intentially possible within the game. This includes but is not limited to accessing unaccessible areas and creating graphic errors or using graphical adjustments.',
            ],
            [
                'title' => 'Harrassment & toxic behavior',
                'text' => 'Do not insult other players via text chat or any other applicable communication channel. We want a friendly and comfortable place for everyone on our server(s). Not following this rule will result into temporary or permanent restrictions, in extreme cases bans.',
            ],
            [
                'title' => 'Cheating or Scripting',
                'text' => 'Do not use any third-party paid or free programs which provide an unfair advantage in-game. This includes scripts which can perform any action that is otherwise not possible to be performed ingame.',
            ],

        ],

	],

	/*
	|--------------------------------------------------------------------------
	| Staff
	|--------------------------------------------------------------------------
	|
	| To improve credibility of your server, you can add a list of staff members
	| here. If avatar is left empty, it will display a default image instead. Upload
	| staff avatars to the img folder and add the filename. Dimensions are 180x180 px.
	| Link can be left empty and it will only show up as normal text. If you input a
	| link, the name of the staff member will be linked to the URL. Link has to start
	| with the protocol (https://).
	|
	*/

	'staff' => [

		'enabled' => 'yes',

		'heading' => 'Staff',

		'navigation' => 'Staff',

		'members' => [

            [
                'name' => 'Ralli',
                'rank' => 'Owner',
                'avatar' => '',
                'link' => '',
            ],
            [
                'name' => 'Maria',
                'rank' => 'Admin',
                'avatar' => '',
                'link' => '',
            ],
            [
                'name' => 'Kevin',
                'rank' => 'Mod',
                'avatar' => '',
                'link' => '',
            ],

        ],

	],

	/*
	|--------------------------------------------------------------------------
	| FAQ
	|--------------------------------------------------------------------------
	|
	| If enabled, it will show your server FAQ on the page.
	| Feel free to add any new faq entries you feel applicable to your server, they
	| will automatically show up on the page. If you want a new line within your
	| text, you can use a <br> (HTML for new line) to start a new line. This will
	| display a new line on the website.
	|
	| Disabled by default, enable by setting enabled to yes
	|
	*/

	'faq' => [

		'enabled' => 'yes',

		'heading' => 'Frequently Asked Questions',

		'navigation' => 'FAQ',

		'entries' => [

            [
                'title' => 'Wipe Times',
                'text' => 'This is an informational part where you can put information about your wipe times.',
            ],
            [
                'title' => 'Server Information',
                'text' => 'If you want to show any additional server information, you can put them here. You can disable FAQ by setting enabled to no if you do not require it.',
            ],

        ],

	],

	/*
	|--------------------------------------------------------------------------
	| Additional navigation
	|--------------------------------------------------------------------------
	|
	| In case you need to add additional links to your navigation which are not
	| there by default, you can add them directly via this list. Make sure that
	| all links start with the protocol (https://) as they will redirect away
	| from the current host. Additional links will only show if enabled is set to yes.
	|
	*/

	'navigation' => [

		'enabled' => 'no',

		'links' => [

			[
				'text' => 'Example 1',
				'link' => 'https://rust.facepunch.com/',
			],
			[
				'text' => 'Example 2',
				'link' => 'https://rust.facepunch.com/',
			],

		],

	],

    /*
	|--------------------------------------------------------------------------
	| Cache Time
	|--------------------------------------------------------------------------
	|
    | The cache defines the time when data is requested via the BattleMetrics or SourceQuery API
    | and temporarily saves it on the server to decrease load on the website when multiple users
    | are on it or multiple servers are regulary requested.
    |
    | The default cache time is 10 minutes and is usually sufficient enough. Minimum is 2, maximum is 60 minutes.
    | Please be careful especially when using multiple servers, as you could hit BM API limits in certain cases.
	|
	*/

    'cacheTime' => [

        'minutes' => 10,

    ],

	/*
	|--------------------------------------------------------------------------
	| Christmas
	|--------------------------------------------------------------------------
	|
	| Christmas mode will add snow to your website, add an additional christmas tree image
	| and will make it a little more festive for the season!
	|
	*/

    'christmasImage' => 'christmas.webp', // Changes when 'Christmas' is enabled below

	'christmas' => 'disabled',

	/*
	|--------------------------------------------------------------------------
	| Simple and Extended Stats
	|--------------------------------------------------------------------------
	|
	| The Simple and Extended stats modules are extra modules for the outpost template. They are separate
	| products and can be purchased on the same marketplace where you purchased this website
	| template. They automatically integrate with the template once setup and
	| will show up in the navigation.
	|
	*/

	'statistics' => [  	// Additional setup will be from the web panel or directly in the /stats/config.php file

		'navigation' => 'Stats',

	],

	'extendedstats' => [ // Additional setup will be from the web panel or directly in the /extstats/config.php file

		'navigation' => 'Stats+',

	],

	/*
	|--------------------------------------------------------------------------
	| Discord and Steam Account Linking
	|--------------------------------------------------------------------------
	|
	| The OutpostLink module is an extra module for the outpost template. It is a separate
	| product and can be purchased on the same marketplace where you purchased this website
	| template. It automatically integrates with the template once it is setup and
	| will show up in the navigation.
	|
	| Additional setup will be from the web panel or directly in the /link/config.php file
	|
	*/

	'link' => [

		'navigation' => 'Link',

	],

	/*
	|--------------------------------------------------------------------------
	| Server Pages
	|--------------------------------------------------------------------------
	|
	| The Server Pages module is an extra module for the outpost template. It is a separate
	| product and can be purchased on the same marketplace where you purchased this website
	| template. It automatically integrates with the template once it is setup and
	| will show up in the navigation.
	|
	| Additional setup will be from the web panel or directly in the /servers/config.php file
	|
	*/

	'serverpages' => [

		'navigation' => 'Servers',

	],

	/*
	|--------------------------------------------------------------------------
	| Vote Links
	|--------------------------------------------------------------------------
	|
	| The Vote Links module is an extra module for the outpost template. It is a separate
	| product and can be purchased on the same marketplace where you purchased this website
	| template. It automatically integrates with the template once it is setup and
	| will show up in the navigation.
	|
	| Additional setup will be from the web panel or directly in the /vote/config.php file
	|
	*/

	'voting' => [

		'navigation' => 'Vote',

	],

	/*
	|--------------------------------------------------------------------------
	| Donate/AltPayments
	|--------------------------------------------------------------------------
	|
	| The Donations module is an extra module for the outpost template. It is a separate
	| product and can be purchased on the same marketplace where you purchased this website
	| template. It automatically integrates with the template once it is setup and
	| will show up in the navigation.
	|
	| Additional setup will be from the web panel or directly in the /donate/config.php file
	|
	*/

	'donate' => [

		'navigation' => 'Donate',
	],

	/*
    |--------------------------------------------------------------------------
    | Bans List
    |--------------------------------------------------------------------------
    |
	| The Bans List module is an extra module for the outpost template. It is a separate
	| product and will be available for purchased on the same marketplace as this website
	| template. It will automatically integrate with the template once it is setup and
	| will appear in the navigation.
	|
	| Additional setup will be from the web panel or directly in the /bans/config.php file
    |
    */

    'banslist' => [

		'navigation' => 'Bans',

	],

    /*
    |--------------------------------------------------------------------------
    | Language
    |--------------------------------------------------------------------------
    |
    | The language used on the website. Only change this if your primary content
    | is not in english.
    |
    */

	'language' => 'en',

];