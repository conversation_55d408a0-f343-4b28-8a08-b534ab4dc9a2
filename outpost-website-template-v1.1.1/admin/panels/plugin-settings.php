<?php
session_start();

/**
 * plugin-settings.php
 * Plugin Management Panel
 * @owner     Modified for Plugin Marketplace
 * @copyright 2024 Plugin Marketplace
 */

if (!isset($_SESSION['authorized']) || !$_SESSION['authorized']) {
    header('Location: ../includes/login.php');
    exit;
}

define('ADMIN_ACCESS', true);

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $config = include '../config.php';
    
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add_plugin':
                $newPlugin = [
                    'id' => count($config['plugins']) + 1,
                    'name' => $_POST['name'],
                    'description' => $_POST['description'],
                    'price' => floatval($_POST['price']),
                    'category' => $_POST['category'],
                    'image' => $_POST['image'],
                    'featured' => isset($_POST['featured']),
                    'file' => $_POST['file'],
                    'version' => $_POST['version'],
                    'author' => $_POST['author'],
                    'compatibility' => $_POST['compatibility'],
                    'tags' => explode(',', $_POST['tags']),
                    'changelog' => $_POST['changelog'],
                    'requirements' => $_POST['requirements']
                ];
                $config['plugins'][] = $newPlugin;
                break;
                
            case 'edit_plugin':
                $pluginId = intval($_POST['plugin_id']);
                foreach ($config['plugins'] as &$plugin) {
                    if ($plugin['id'] == $pluginId) {
                        $plugin['name'] = $_POST['name'];
                        $plugin['description'] = $_POST['description'];
                        $plugin['price'] = floatval($_POST['price']);
                        $plugin['category'] = $_POST['category'];
                        $plugin['image'] = $_POST['image'];
                        $plugin['featured'] = isset($_POST['featured']);
                        $plugin['file'] = $_POST['file'];
                        $plugin['version'] = $_POST['version'];
                        $plugin['author'] = $_POST['author'];
                        $plugin['compatibility'] = $_POST['compatibility'];
                        $plugin['tags'] = explode(',', $_POST['tags']);
                        $plugin['changelog'] = $_POST['changelog'];
                        $plugin['requirements'] = $_POST['requirements'];
                        break;
                    }
                }
                break;
                
            case 'delete_plugin':
                $pluginId = intval($_POST['plugin_id']);
                $config['plugins'] = array_filter($config['plugins'], function($plugin) use ($pluginId) {
                    return $plugin['id'] != $pluginId;
                });
                $config['plugins'] = array_values($config['plugins']); // Re-index array
                break;
        }
        
        // Save updated config
        $configContent = "<?php\n\nreturn " . var_export($config, true) . ";\n";
        file_put_contents('../config.php', $configContent);
        
        echo '<div class="alert alert-success">Plugin settings updated successfully!</div>';
    }
}

$config = include '../config.php';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Plugin Management - Admin Panel</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="../assets/css/dashboard.css">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="../dashboard.php">Admin Panel</a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="../dashboard.php">Dashboard</a>
                <a class="nav-link" href="../includes/logout.php">Logout</a>
            </div>
        </div>
    </nav>

<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <h2>Plugin Management</h2>
            <p class="text-muted">Manage your Rust plugins, add new ones, edit existing plugins, and configure plugin settings.</p>
        </div>
    </div>

    <!-- Add New Plugin -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>Add New Plugin</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <input type="hidden" name="action" value="add_plugin">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Plugin Name</label>
                                    <input type="text" class="form-control" id="name" name="name" required>
                                </div>
                                <div class="mb-3">
                                    <label for="price" class="form-label">Price ($)</label>
                                    <input type="number" step="0.01" class="form-control" id="price" name="price" required>
                                </div>
                                <div class="mb-3">
                                    <label for="category" class="form-label">Category</label>
                                    <select class="form-control" id="category" name="category" required>
                                        <?php foreach ($config['pluginCategories'] as $category): ?>
                                            <option value="<?php echo htmlspecialchars($category); ?>"><?php echo htmlspecialchars($category); ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="image" class="form-label">Image Filename</label>
                                    <input type="text" class="form-control" id="image" name="image" placeholder="plugin-image.webp">
                                </div>
                                <div class="mb-3">
                                    <label for="file" class="form-label">Plugin File</label>
                                    <input type="text" class="form-control" id="file" name="file" placeholder="PluginName.cs" required>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="featured" name="featured">
                                        <label class="form-check-label" for="featured">Featured Plugin</label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="description" class="form-label">Description</label>
                                    <textarea class="form-control" id="description" name="description" rows="3" required></textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="version" class="form-label">Version</label>
                                    <input type="text" class="form-control" id="version" name="version" placeholder="1.0.0" required>
                                </div>
                                <div class="mb-3">
                                    <label for="author" class="form-label">Author</label>
                                    <input type="text" class="form-control" id="author" name="author" required>
                                </div>
                                <div class="mb-3">
                                    <label for="compatibility" class="form-label">Compatibility</label>
                                    <input type="text" class="form-control" id="compatibility" name="compatibility" placeholder="Oxide/uMod" required>
                                </div>
                                <div class="mb-3">
                                    <label for="tags" class="form-label">Tags (comma separated)</label>
                                    <input type="text" class="form-control" id="tags" name="tags" placeholder="admin, utility, teleport">
                                </div>
                                <div class="mb-3">
                                    <label for="changelog" class="form-label">Changelog</label>
                                    <textarea class="form-control" id="changelog" name="changelog" rows="2"></textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="requirements" class="form-label">Requirements</label>
                                    <textarea class="form-control" id="requirements" name="requirements" rows="2"></textarea>
                                </div>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary">Add Plugin</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Existing Plugins -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>Existing Plugins</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($config['plugins'])): ?>
                        <p class="text-muted">No plugins configured yet. Add your first plugin above!</p>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Price</th>
                                        <th>Category</th>
                                        <th>Version</th>
                                        <th>Featured</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($config['plugins'] as $plugin): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($plugin['name']); ?></td>
                                            <td>$<?php echo number_format($plugin['price'], 2); ?></td>
                                            <td><?php echo htmlspecialchars($plugin['category']); ?></td>
                                            <td><?php echo htmlspecialchars($plugin['version']); ?></td>
                                            <td><?php echo $plugin['featured'] ? 'Yes' : 'No'; ?></td>
                                            <td>
                                                <button class="btn btn-sm btn-warning" onclick="editPlugin(<?php echo $plugin['id']; ?>)">Edit</button>
                                                <form method="POST" style="display: inline;">
                                                    <input type="hidden" name="action" value="delete_plugin">
                                                    <input type="hidden" name="plugin_id" value="<?php echo $plugin['id']; ?>">
                                                    <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this plugin?')">Delete</button>
                                                </form>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
    function editPlugin(pluginId) {
        // This would open a modal or redirect to an edit form
        // For now, we'll just alert the user
        alert('Edit functionality would be implemented here for plugin ID: ' + pluginId);
    }
    </script>
</body>
</html>
