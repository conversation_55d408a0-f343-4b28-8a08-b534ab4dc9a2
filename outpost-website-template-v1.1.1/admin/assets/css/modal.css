@font-face {
    font-family: 'Roboto Condensed';
    font-style: normal;
    font-weight: 300;
    font-display: swap;
    src: url(../fonts/Roboto-Condensed-Light.woff2) format('woff2');
}

@font-face {
    font-family: 'Roboto Condensed';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url(../fonts/Roboto-Condensed-Regular.woff2) format('woff2');
}

@font-face {
    font-family: 'Roboto Condensed';
    font-style: normal;
    font-weight: 700;
    font-display: swap;
    src: url(../fonts/Roboto-Condensed-Bold.woff2) format('woff2');
}

.modal {
    display: none;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
    width: 80%;
    max-width: 500px;
    background-color: rgba(32, 36, 32, 0.95);
    border: 1px solid rgb(61, 75, 39);
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    padding: 20px;
    color: #f2f2f2;
    font-family: "Roboto Condensed", Arial, sans-serif;
}

.modal h2 {
    margin: 0 0 10px;
    font-size: 18px;
    color: #fff;
    text-align: center;
}

.modal-content {
    overflow-y: auto;
    max-height: 300px;
}

.file-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
}

.file-item {
    padding: 10px;
    background-color: #444;
    color: #f2f2f2;
    border: 1px solid rgb(61, 75, 39);
    border-radius: 4px;
    cursor: pointer;
    text-align: center;
    transition: background 0.2s ease;
}

.file-item:hover {
    background-color: rgb(88, 108, 57);
}

.close-modal {
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 20px;
    color: #fff;
    cursor: pointer;
}

.close-modal:hover {
    color: rgb(172, 56, 39);
}
