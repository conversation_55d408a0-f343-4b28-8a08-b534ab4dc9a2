@font-face {
    font-family: 'Roboto Condensed';
    font-style: normal;
    font-weight: 300;
    font-display: swap;
    src: url(../fonts/Roboto-Condensed-Light.woff2) format('woff2');
}

@font-face {
    font-family: 'Roboto Condensed';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url(../fonts/Roboto-Condensed-Regular.woff2) format('woff2');
}

@font-face {
    font-family: 'Roboto Condensed';
    font-style: normal;
    font-weight: 700;
    font-display: swap;
    src: url(../fonts/Roboto-Condensed-Bold.woff2) format('woff2');
}

body {
    color: rgb(220, 211, 202);
    font-family: "Roboto Condensed", Arial, sans-serif;
    margin: 0;
    padding: 20px;
}
.container {
    max-width: 100%;
    margin: 20px auto;
    padding: 20px;
}
h1, h2 {
    text-align: center;
    color: #fff;
}

.form-group {
    margin-bottom: 15px;
}
.form-label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}
.form-input {
    width: 100%;
    padding: 10px;
    border: 1px solid rgb(61, 75, 39);
    border-radius: 2px;
    font-size: 14px;
    background: #444;
    color: #fff;
}
.form-input[type="checkbox"] {
    width: auto;
}
.form-description {
    font-size: 12px;
    color: #ccc;
    margin-bottom: 5px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}
.section {
    margin-bottom: 20px;
    border: 1px solid rgb(61, 75, 39);
    border-radius: 2px;
    padding: 10px;
    background-color: rgba(32, 36, 32, 0.75) !important;
}
.section h2 {
    margin-top: 0;
    font-size: 18px;
    padding: 10px;
    border-bottom: 1px solid #ddd;
}

.btn {
    transition: background 0.2s ease;
    border-radius: 0;
    border: 0;
    padding: 8px 14px;
    font-weight: bold;
    text-transform: uppercase;
    font-size: 18px;
    cursor: pointer;
    margin-top: 5px;
}
.btn.btn-primary {
    background: rgb(61, 75, 39);
    color: rgb(166, 205, 99);
}
.btn.btn-primary:hover {
    background: rgb(88, 108, 57);
}
.btn.btn-secondary {
    background: rgb(29, 66, 95);
    color: rgb(72, 154, 212);
}
.btn.btn-secondary:hover {
    background: rgb(39, 85, 121);
}
.btn.btn-danger {
    background: rgb(150, 47, 32);
    color: rgb(199, 152, 151);
}
.btn.btn-danger:hover {
    background: rgb(172, 56, 39);
}

.alert {
    padding: 10px;
    margin-bottom: 20px;
    border-radius: 4px;
    font-weight: bold;
    text-align: center;
    opacity: 1;
    transition: opacity 1s ease-out;
}
.alert-success {
    background-color: rgb(61, 75, 39);
    color:#fff;
}
.alert-danger {
    background-color: rgb(172, 56, 39);
    color:#fff;
}
.fade-out {
    opacity: 0;
}

.faq-cards-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-top: 20px;
}
.faq-card {
    border: 1px solid rgb(61, 75, 39);
    border-radius: 2px;
    padding: 20px;
    width: calc(100% - 20px);
    background-color: rgba(32, 36, 32, 0.95) !important;
}
.faq-card h3 {
    margin-top: 0;
    font-size: 18px;
    color: #f2f2f2;
}
.faq-card .form-group {
    margin-bottom: 10px;
}
.faq-card .form-input {
    width: 100%;
    padding: 10px;
    border: 1px solid rgb(61, 75, 39);
    border-radius: 4px;
}
@media screen and (max-width: 1024px) {
    .faq-card {
        width: calc(50% - 20px); /* Two cards per row on medium screens */
    }
}
@media screen and (max-width: 768px) {
    .faq-card {
        width: 100%; /* One card per row on smaller screens */
    }
}

.rules-cards-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-top: 20px;
}
.rule-card {
    border: 1px solid rgb(61, 75, 39);
    border-radius: 2px;
    padding: 20px;
    width: calc(100% - 20px);
    background-color: rgba(32, 36, 32, 0.95) !important;
}
.rule-card h3 {
    margin-top: 0;
    font-size: 18px;
    color: #f2f2f2;
}
.rule-card .form-group {
    margin-bottom: 10px;
}
.rule-card .form-input {
    width: 100%;
    padding: 10px;
    border: 1px solid rgb(61, 75, 39);
    border-radius: 4px;
}
@media screen and (max-width: 1024px) {
    .rule-card {
        width: calc(50% - 20px);
    }
}
@media screen and (max-width: 768px) {
    .rule-card {
        width: 100%;
    }
}

.server-cards-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-top: 20px;
}
.server-card {
    border: 1px solid rgb(61, 75, 39);
    border-radius: 2px;
    padding: 20px;
    width: calc(30% - 20px);
    background-color: rgba(32, 36, 32, 0.95) !important;
}
.server-card h3 {
    margin-top: 0;
    font-size: 18px;
    color: #f2f2f2;
}
.server-card .form-group {
    margin-bottom: 10px;
}
.server-card .form-input {
    width: 100%;
    padding: 10px;
    border: 1px solid rgb(61, 75, 39);
    border-radius: 4px;
}
@media screen and (max-width: 1024px) {
    .server-card {
        width: calc(50% - 20px);
    }
}
@media screen and (max-width: 768px) {
    .server-card {
        width: 100%;
    }
}

.staff-cards-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-top: 20px;
}
.staff-card {
    border: 1px solid rgb(61, 75, 39);
    border-radius: 2px;
    padding: 20px;
    width: calc(30% - 20px);
    background-color: rgba(32, 36, 32, 0.95) !important;
}
.staff-card h3 {
    margin-top: 0;
    font-size: 18px;
    color: #f2f2f2;
}
.staff-card .form-group {
    margin-bottom: 10px;
}
.staff-card .form-input {
    width: 100%;
    padding: 10px;
    border: 1px solid rgb(61, 75, 39);
    border-radius: 4px;
}
@media screen and (max-width: 1024px) {
    .staff-card {
        width: calc(50% - 20px); /* Two cards per row on medium screens */
    }
}
@media screen and (max-width: 768px) {
    .staff-card {
        width: 100%; /* One card per row on smaller screens */
    }
}





