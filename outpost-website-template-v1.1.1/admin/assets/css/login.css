/* ========================================
 *        Custom Additions for Media Pages
 * ========================================
 */

 @font-face {
    font-family: 'Roboto Condensed';
    font-style: normal;
    font-weight: 300;
    font-display: swap;
    src: url(../fonts/Roboto-Condensed-Light.woff2) format('woff2');
}

@font-face {
    font-family: 'Roboto Condensed';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url(../fonts/Roboto-Condensed-Regular.woff2) format('woff2');
}

@font-face {
    font-family: 'Roboto Condensed';
    font-style: normal;
    font-weight: 700;
    font-display: swap;
    src: url(../fonts/Roboto-Condensed-Bold.woff2) format('woff2');
}

body {
    font-family: "Roboto Condensed", Arial, sans-serif;
    color: rgb(220, 211, 202);
}

.login-container {
    text-align: center;
    margin-top: 25px;
}

.user-info-box {
    display: inline-block;
    padding: 15px;
    border: 1px solid rgb(61, 75, 39);
    border-radius: 4px;
    background: rgba(32, 36, 32, 0.95) !important;
    text-align: center;
    margin-bottom: 20px;
}

.user-info-box img {
    border-radius: 75%;
    width: 120px;
    height: 120px;
}

.user-info-box h2 {
    margin: 10px 0 5px;
    font-size: 1.5em;
}

.btn {
    transition: background 0.2s ease;
    border-radius: 0;
    border: 0;
    padding: 8px 14px;
    font-weight: bold;
    text-transform: uppercase;
    font-size: 18px;
    cursor: pointer;
    margin-top: 5px;
}
.btn.btn-primary {
    background: rgb(61, 75, 39);
    color: rgb(166, 205, 99);
}
.btn.btn-primary:hover {
    background: rgb(88, 108, 57);
}
