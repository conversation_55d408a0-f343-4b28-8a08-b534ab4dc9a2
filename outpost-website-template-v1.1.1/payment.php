<?php
session_start();

/**
 * payment.php
 * Payment Processing Handler
 * @owner     Plugin Marketplace
 * @copyright 2024 Plugin Marketplace
 */

require_once __DIR__ . '/plugin-core.php';

// Check if user is logged in
if (!isset($_SESSION['steamid'])) {
    http_response_code(401);
    die(json_encode(['error' => 'You must be logged in to purchase plugins.']));
}

// Get POST data
$input = json_decode(file_get_contents('php://input'), true);
$pluginId = $input['plugin_id'] ?? '';
$paymentMethod = $input['payment_method'] ?? '';
$userEmail = $input['email'] ?? '';

if (empty($pluginId) || empty($paymentMethod) || empty($userEmail)) {
    http_response_code(400);
    die(json_encode(['error' => 'Missing required parameters.']));
}

// Get plugin information
$plugin = getPluginById($config, $pluginId);
if (!$plugin) {
    http_response_code(404);
    die(json_encode(['error' => 'Plugin not found.']));
}

// Validate email
if (!filter_var($userEmail, FILTER_VALIDATE_EMAIL)) {
    http_response_code(400);
    die(json_encode(['error' => 'Invalid email address.']));
}

// Process payment based on method
switch ($paymentMethod) {
    case 'paypal':
        $paymentResult = processPayPalPayment($plugin, $userEmail);
        break;
    case 'stripe':
        $paymentResult = processStripePayment($plugin, $userEmail);
        break;
    case 'crypto':
        $paymentResult = processCryptoPayment($plugin, $userEmail);
        break;
    default:
        http_response_code(400);
        die(json_encode(['error' => 'Invalid payment method.']));
}

if ($paymentResult['success']) {
    // Generate download token
    $downloadToken = generateDownloadToken($plugin['id'], $userEmail);
    
    // Log the purchase
    logPurchase($plugin, $userEmail, $plugin['price'], $paymentMethod);
    
    // Send success response with download link
    $downloadUrl = 'download.php?token=' . urlencode($downloadToken) . '&plugin=' . urlencode($plugin['id']);
    
    echo json_encode([
        'success' => true,
        'message' => 'Payment successful! Your download will begin shortly.',
        'download_url' => $downloadUrl,
        'plugin_name' => $plugin['name']
    ]);
} else {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $paymentResult['error']
    ]);
}

/**
 * Process PayPal payment
 */
function processPayPalPayment($plugin, $userEmail) {
    global $config;

    // PayPal API configuration
    $paypalConfig = $config['store']['paypal'];

    if (!$paypalConfig['enabled']) {
        return ['success' => false, 'error' => 'PayPal payments are disabled'];
    }

    // For demo purposes, we'll simulate a successful payment
    // In a real implementation, you would use PayPal SDK:
    /*
    require_once 'vendor/autoload.php';
    use PayPal\Api\Amount;
    use PayPal\Api\Payer;
    use PayPal\Api\Payment;
    use PayPal\Api\PaymentExecution;
    use PayPal\Api\RedirectUrls;
    use PayPal\Api\Transaction;
    use PayPal\Auth\OAuthTokenCredential;
    use PayPal\Rest\ApiContext;

    $apiContext = new ApiContext(
        new OAuthTokenCredential(
            $paypalConfig['client_id'],
            $paypalConfig['client_secret']
        )
    );

    $apiContext->setConfig([
        'mode' => $paypalConfig['sandbox'] ? 'sandbox' : 'live'
    ]);

    $payer = new Payer();
    $payer->setPaymentMethod('paypal');

    $amount = new Amount();
    $amount->setCurrency('USD')
           ->setTotal($plugin['price']);

    $transaction = new Transaction();
    $transaction->setAmount($amount)
                ->setDescription($plugin['name'] . ' - Rust Plugin');

    $redirectUrls = new RedirectUrls();
    $redirectUrls->setReturnUrl($config['store']['return_url'])
                 ->setCancelUrl($config['store']['cancel_url']);

    $payment = new Payment();
    $payment->setIntent('sale')
            ->setPayer($payer)
            ->setRedirectUrls($redirectUrls)
            ->setTransactions([$transaction]);

    try {
        $payment->create($apiContext);
        return [
            'success' => true,
            'redirect_url' => $payment->getApprovalLink(),
            'payment_id' => $payment->getId()
        ];
    } catch (Exception $ex) {
        return [
            'success' => false,
            'error' => 'PayPal payment creation failed: ' . $ex->getMessage()
        ];
    }
    */

    // Demo simulation
    return [
        'success' => true,
        'transaction_id' => 'PP_' . uniqid(),
        'payment_method' => 'paypal'
    ];
}

/**
 * Process Stripe payment
 */
function processStripePayment($plugin, $userEmail) {
    global $config;

    // Stripe API configuration
    $stripeConfig = $config['store']['stripe'];

    if (!$stripeConfig['enabled']) {
        return ['success' => false, 'error' => 'Stripe payments are disabled'];
    }

    // For demo purposes, we'll simulate a successful payment
    // In a real implementation, you would use Stripe SDK:
    /*
    require_once 'vendor/autoload.php';
    \Stripe\Stripe::setApiKey($stripeConfig['secret_key']);

    try {
        $paymentIntent = \Stripe\PaymentIntent::create([
            'amount' => $plugin['price'] * 100, // Stripe uses cents
            'currency' => 'usd',
            'description' => $plugin['name'] . ' - Rust Plugin',
            'receipt_email' => $userEmail,
            'metadata' => [
                'plugin_id' => $plugin['id'],
                'plugin_name' => $plugin['name']
            ]
        ]);

        return [
            'success' => true,
            'client_secret' => $paymentIntent->client_secret,
            'payment_intent_id' => $paymentIntent->id
        ];
    } catch (\Stripe\Exception\CardException $e) {
        return [
            'success' => false,
            'error' => 'Card error: ' . $e->getError()->message
        ];
    } catch (\Stripe\Exception\RateLimitException $e) {
        return [
            'success' => false,
            'error' => 'Too many requests made to the API too quickly'
        ];
    } catch (\Stripe\Exception\InvalidRequestException $e) {
        return [
            'success' => false,
            'error' => 'Invalid parameters: ' . $e->getError()->message
        ];
    } catch (\Stripe\Exception\AuthenticationException $e) {
        return [
            'success' => false,
            'error' => 'Authentication with Stripe\'s API failed'
        ];
    } catch (\Stripe\Exception\ApiConnectionException $e) {
        return [
            'success' => false,
            'error' => 'Network communication with Stripe failed'
        ];
    } catch (\Stripe\Exception\ApiErrorException $e) {
        return [
            'success' => false,
            'error' => 'Stripe API error: ' . $e->getError()->message
        ];
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => 'An unexpected error occurred'
        ];
    }
    */

    // Demo simulation
    return [
        'success' => true,
        'transaction_id' => 'ST_' . uniqid(),
        'payment_method' => 'stripe'
    ];
}

/**
 * Process cryptocurrency payment
 */
function processCryptoPayment($plugin, $userEmail) {
    global $config;

    // Crypto payment configuration
    $cryptoConfig = $config['store']['crypto'];

    if (!$cryptoConfig['enabled']) {
        return ['success' => false, 'error' => 'Cryptocurrency payments are disabled'];
    }

    // For demo purposes, we'll simulate a successful payment
    // In a real implementation, you would integrate with services like:
    // - CoinGate
    // - BitPay
    // - Coinbase Commerce
    // - BTCPay Server

    /*
    // Example with CoinGate API:
    $coingate = new \CoinGate\CoinGate([
        'environment' => $cryptoConfig['sandbox'] ? 'sandbox' : 'live',
        'auth_token' => $cryptoConfig['api_token']
    ]);

    try {
        $order = $coingate->order->create([
            'order_id' => 'plugin_' . $plugin['id'] . '_' . time(),
            'price_amount' => $plugin['price'],
            'price_currency' => 'USD',
            'receive_currency' => $cryptoConfig['receive_currency'], // BTC, ETH, etc.
            'title' => $plugin['name'],
            'description' => 'Rust Plugin Purchase',
            'callback_url' => $config['store']['crypto_callback_url'],
            'cancel_url' => $config['store']['cancel_url'],
            'success_url' => $config['store']['success_url'],
            'buyer_email' => $userEmail
        ]);

        return [
            'success' => true,
            'payment_url' => $order->payment_url,
            'order_id' => $order->id
        ];
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => 'Crypto payment creation failed: ' . $e->getMessage()
        ];
    }
    */

    // Demo simulation
    return [
        'success' => true,
        'transaction_id' => 'CR_' . uniqid(),
        'payment_method' => 'crypto'
    ];
}

/**
 * Log purchase transaction
 */
function logPurchase($plugin, $userEmail, $amount, $paymentMethod) {
    $logEntry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'plugin_id' => $plugin['id'],
        'plugin_name' => $plugin['name'],
        'user_email' => $userEmail,
        'amount' => $amount,
        'payment_method' => $paymentMethod,
        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
    ];

    $logFile = 'logs/purchases.log';
    if (!file_exists('logs')) {
        mkdir('logs', 0755, true);
    }
    file_put_contents($logFile, json_encode($logEntry) . "\n", FILE_APPEND | LOCK_EX);
}

?>
