<?php
session_start();

/**
 * payment-success.php
 * Payment Success Page
 * @owner     Plugin Marketplace
 * @copyright 2024 Plugin Marketplace
 */

require_once __DIR__ . '/plugin-core.php';

// Check if user is logged in
if (!isset($_SESSION['steamid'])) {
    header('Location: index.php');
    exit;
}

// Get payment details from URL parameters
$paymentId = $_GET['payment_id'] ?? '';
$token = $_GET['token'] ?? '';
$pluginId = $_GET['plugin_id'] ?? '';

$plugin = null;
if ($pluginId) {
    $plugin = getPluginById($config, $pluginId);
}

include 'templates/head.php';
include 'templates/navigation.php';
?>

<div class="container">
    <main>
        <section class="payment-success py-5">
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="card bg-dark text-white">
                        <div class="card-header bg-success">
                            <h3 class="mb-0"><i class="fas fa-check-circle"></i> Payment Successful!</h3>
                        </div>
                        <div class="card-body">
                            <?php if ($plugin): ?>
                                <h4>Thank you for purchasing: <?php echo htmlspecialchars($plugin['name']); ?></h4>
                                <p class="lead">Your payment has been processed successfully.</p>
                                
                                <div class="alert alert-info">
                                    <h5><i class="fas fa-download"></i> Download Instructions</h5>
                                    <p>Your download link has been sent to your email address. The link will be valid for <?php echo $config['store']['downloadExpiry']; ?> days.</p>
                                    <p>If you don't receive the email within a few minutes, please check your spam folder.</p>
                                </div>
                                
                                <div class="plugin-details mt-4">
                                    <h5>Plugin Details:</h5>
                                    <ul class="list-unstyled">
                                        <li><strong>Name:</strong> <?php echo htmlspecialchars($plugin['name']); ?></li>
                                        <li><strong>Version:</strong> <?php echo htmlspecialchars($plugin['version']); ?></li>
                                        <li><strong>Author:</strong> <?php echo htmlspecialchars($plugin['author']); ?></li>
                                        <li><strong>Category:</strong> <?php echo htmlspecialchars($plugin['category']); ?></li>
                                        <li><strong>File:</strong> <?php echo htmlspecialchars($plugin['file']); ?></li>
                                    </ul>
                                </div>
                                
                                <?php if (!empty($plugin['requirements'])): ?>
                                <div class="requirements mt-3">
                                    <h5>Requirements:</h5>
                                    <p><?php echo htmlspecialchars($plugin['requirements']); ?></p>
                                </div>
                                <?php endif; ?>
                                
                            <?php else: ?>
                                <h4>Payment Confirmed</h4>
                                <p class="lead">Your payment has been processed successfully.</p>
                                <p>You should receive a confirmation email shortly with your download instructions.</p>
                            <?php endif; ?>
                            
                            <div class="transaction-info mt-4">
                                <h5>Transaction Information:</h5>
                                <ul class="list-unstyled">
                                    <?php if ($paymentId): ?>
                                    <li><strong>Payment ID:</strong> <?php echo htmlspecialchars($paymentId); ?></li>
                                    <?php endif; ?>
                                    <li><strong>Date:</strong> <?php echo date('Y-m-d H:i:s'); ?></li>
                                    <li><strong>Status:</strong> <span class="text-success">Completed</span></li>
                                </ul>
                            </div>
                            
                            <div class="support-info mt-4">
                                <h5>Need Help?</h5>
                                <p>If you have any issues with your purchase or need support, please contact us at:</p>
                                <p><strong>Email:</strong> <a href="mailto:<?php echo $config['store']['supportEmail'] ?? '<EMAIL>'; ?>"><?php echo $config['store']['supportEmail'] ?? '<EMAIL>'; ?></a></p>
                            </div>
                        </div>
                        <div class="card-footer">
                            <div class="d-flex justify-content-between">
                                <a href="index.php" class="btn btn-primary">
                                    <i class="fas fa-home"></i> Back to Store
                                </a>
                                <a href="index.php#plugins" class="btn btn-outline-light">
                                    <i class="fas fa-shopping-cart"></i> Browse More Plugins
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>
</div>

<?php include 'templates/footer.php'; ?>

<style>
.payment-success {
    min-height: 60vh;
    display: flex;
    align-items: center;
}

.card {
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.card-header.bg-success {
    background: linear-gradient(135deg, #28a745, #20c997) !important;
}

.alert-info {
    background: rgba(23, 162, 184, 0.2);
    border: 1px solid rgba(23, 162, 184, 0.5);
    color: #fff;
}

.plugin-details ul li,
.transaction-info ul li {
    padding: 0.25rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.plugin-details ul li:last-child,
.transaction-info ul li:last-child {
    border-bottom: none;
}
</style>
