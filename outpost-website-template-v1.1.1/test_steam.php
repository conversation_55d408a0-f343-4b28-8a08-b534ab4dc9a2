<?php
session_start();
require_once 'steamauth/steamauth.php';
?>
<!DOCTYPE html>
<html>
<head>
    <title>Steam Authentication Test</title>
    <link rel="stylesheet" href="assets/css/bootstrap.min.css">
    <style>
        body { padding: 20px; }
        .steam-info { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Steam Authentication Test</h1>
        
        <?php if (!isset($_SESSION['steamid'])): ?>
            <div class="alert alert-info">
                <h4>Not logged in</h4>
                <p>Click the button below to test Steam authentication:</p>
                <?php loginbutton("rectangle"); ?>
            </div>
        <?php else: ?>
            <div class="alert alert-success">
                <h4>✓ Steam Authentication Successful!</h4>
            </div>
            
            <div class="steam-info">
                <h3>Steam User Information:</h3>
                <p><strong>Steam ID:</strong> <?php echo $_SESSION['steamid']; ?></p>
                <p><strong>Profile Name:</strong> <?php echo $_SESSION['steam_personaname']; ?></p>
                <p><strong>Profile URL:</strong> <a href="<?php echo $_SESSION['steam_profileurl']; ?>" target="_blank"><?php echo $_SESSION['steam_profileurl']; ?></a></p>
                <p><strong>Avatar:</strong></p>
                <img src="<?php echo $_SESSION['steam_avatar']; ?>" alt="Steam Avatar" class="img-thumbnail" style="max-width: 100px;">
            </div>
            
            <div class="mt-3">
                <?php logoutbutton(); ?>
            </div>
        <?php endif; ?>
        
        <hr>
        <div class="mt-4">
            <h3>Configuration Check:</h3>
            <?php 
            require_once 'steamauth/SteamConfig.php';
            echo "<p><strong>Steam API Key:</strong> " . (!empty($steamauth['apikey']) ? '✓ Configured' : '✗ Missing') . "</p>";
            echo "<p><strong>Domain:</strong> " . $steamauth['domainname'] . "</p>";
            ?>
        </div>
        
        <div class="mt-4">
            <a href="index.php" class="btn btn-primary">← Back to Marketplace</a>
        </div>
    </div>
</body>
</html>
