<?php

/**
 * plugin-core.php
 * Plugin Management Core Functions
 * @owner     Plugin Marketplace
 * @copyright 2024 Plugin Marketplace
 */

date_default_timezone_set('UTC');

$config = require __DIR__ . '/config.php';

/**
 * Get all plugins with optional filtering
 *
 * @param array $config
 * @param string|null $category
 * @param bool $featuredOnly
 * @return array
 */
function getPlugins(array $config, $category = null, $featuredOnly = false, $sortBy = 'name')
{
    $plugins = $config['plugins'];

    // Filter by category if specified
    if ($category && $category !== 'all') {
        $plugins = array_filter($plugins, function($plugin) use ($category) {
            return $plugin['category'] === $category;
        });
    }

    // Filter by featured if specified
    if ($featuredOnly) {
        $plugins = array_filter($plugins, function($plugin) {
            return $plugin['featured'] === true;
        });
    }

    // Sort plugins
    switch ($sortBy) {
        case 'price_low':
            usort($plugins, function($a, $b) {
                return $a['price'] <=> $b['price'];
            });
            break;
        case 'price_high':
            usort($plugins, function($a, $b) {
                return $b['price'] <=> $a['price'];
            });
            break;
        case 'newest':
            usort($plugins, function($a, $b) {
                $dateA = isset($a['date_added']) ? strtotime($a['date_added']) : 0;
                $dateB = isset($b['date_added']) ? strtotime($b['date_added']) : 0;
                return $dateB <=> $dateA;
            });
            break;
        case 'name':
        default:
            usort($plugins, function($a, $b) {
                return strcasecmp($a['name'], $b['name']);
            });
            break;
    }

    return $plugins;
}

/**
 * Get a single plugin by ID
 *
 * @param array $config
 * @param int $pluginId
 * @return array|null
 */
function getPluginById(array $config, $pluginId)
{
    foreach ($config['plugins'] as $plugin) {
        if ($plugin['id'] == $pluginId) {
            return $plugin;
        }
    }
    return null;
}

/**
 * Get plugins by category
 *
 * @param array $config
 * @return array
 */
function getPluginsByCategory(array $config)
{
    $categorized = [];
    
    foreach ($config['pluginCategories'] as $category) {
        $categorized[$category] = [];
    }
    
    foreach ($config['plugins'] as $plugin) {
        if (isset($categorized[$plugin['category']])) {
            $categorized[$plugin['category']][] = $plugin;
        }
    }
    
    return $categorized;
}

/**
 * Search plugins by name, description, or tags
 *
 * @param array $config
 * @param string $searchTerm
 * @return array
 */
function searchPlugins(array $config, $searchTerm)
{
    $searchTerm = strtolower($searchTerm);
    $results = [];
    
    foreach ($config['plugins'] as $plugin) {
        $searchableText = strtolower($plugin['name'] . ' ' . $plugin['description'] . ' ' . implode(' ', $plugin['tags']));
        
        if (strpos($searchableText, $searchTerm) !== false) {
            $results[] = $plugin;
        }
    }
    
    return $results;
}

/**
 * Get featured plugins
 *
 * @param array $config
 * @param int $limit
 * @return array
 */
function getFeaturedPlugins(array $config, $limit = 3)
{
    $featured = array_filter($config['plugins'], function($plugin) {
        return $plugin['featured'] === true;
    });
    
    return array_slice($featured, 0, $limit);
}

/**
 * Format price for display
 *
 * @param float $price
 * @param string $currency
 * @return string
 */
function formatPrice($price, $currency = 'USD')
{
    switch ($currency) {
        case 'USD':
            return '$' . number_format($price, 2);
        case 'EUR':
            return '€' . number_format($price, 2);
        case 'GBP':
            return '£' . number_format($price, 2);
        default:
            return number_format($price, 2) . ' ' . $currency;
    }
}

/**
 * Generate download token for purchased plugin
 *
 * @param int $pluginId
 * @param string $userEmail
 * @return string
 */
function generateDownloadToken($pluginId, $userEmail)
{
    $data = $pluginId . '|' . $userEmail . '|' . time();
    return base64_encode($data);
}

/**
 * Validate download token
 *
 * @param string $token
 * @param int $expiryDays
 * @return array|false
 */
function validateDownloadToken($token, $expiryDays = 30)
{
    $decoded = base64_decode($token);
    $parts = explode('|', $decoded);
    
    if (count($parts) !== 3) {
        return false;
    }
    
    list($pluginId, $userEmail, $timestamp) = $parts;
    
    // Check if token has expired
    $expiryTime = $timestamp + ($expiryDays * 24 * 60 * 60);
    if (time() > $expiryTime) {
        return false;
    }
    
    return [
        'plugin_id' => $pluginId,
        'user_email' => $userEmail,
        'timestamp' => $timestamp
    ];
}

/**
 * Log purchase for analytics
 *
 * @param array $plugin
 * @param string $userEmail
 * @param float $amount
 * @param string $paymentMethod
 */
function logPurchase($plugin, $userEmail, $amount, $paymentMethod)
{
    $logEntry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'plugin_id' => $plugin['id'],
        'plugin_name' => $plugin['name'],
        'user_email' => $userEmail,
        'amount' => $amount,
        'payment_method' => $paymentMethod,
        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
    ];
    
    $logFile = 'logs/purchases.log';
    
    // Create logs directory if it doesn't exist
    if (!file_exists('logs')) {
        mkdir('logs', 0755, true);
    }
    
    file_put_contents($logFile, json_encode($logEntry) . "\n", FILE_APPEND | LOCK_EX);
}

/**
 * Get plugin statistics
 *
 * @param array $config
 * @return array
 */
function getPluginStats($config)
{
    $stats = [
        'total_plugins' => count($config['plugins']),
        'featured_plugins' => count(array_filter($config['plugins'], function($p) { return $p['featured']; })),
        'categories' => count($config['pluginCategories']),
        'average_price' => 0
    ];
    
    if ($stats['total_plugins'] > 0) {
        $totalPrice = array_sum(array_column($config['plugins'], 'price'));
        $stats['average_price'] = $totalPrice / $stats['total_plugins'];
    }
    
    return $stats;
}

// Initialize plugins data
$plugins = getPlugins($config);
$featuredPlugins = getFeaturedPlugins($config);
$pluginsByCategory = getPluginsByCategory($config);
$pluginStats = getPluginStats($config);

// Handle search if provided
if (isset($_GET['search']) && !empty($_GET['search'])) {
    $plugins = searchPlugins($config, $_GET['search']);
}

// Handle category filter if provided
if (isset($_GET['category']) && !empty($_GET['category'])) {
    $plugins = getPlugins($config, $_GET['category']);
}

?>
