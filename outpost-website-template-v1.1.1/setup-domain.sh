#!/bin/bash

# setup-domain.sh
# Automated domain setup script for Rust Plugin Marketplace
# Usage: sudo ./setup-domain.sh your-domain.com

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[SETUP]${NC} $1"
}

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   print_error "This script must be run as root (use sudo)"
   exit 1
fi

# Check if domain is provided
if [ -z "$1" ]; then
    print_error "Usage: sudo ./setup-domain.sh your-domain.com"
    exit 1
fi

DOMAIN=$1
WEB_ROOT="/var/www/$DOMAIN"
NGINX_CONFIG="/etc/nginx/sites-available/$DOMAIN"

print_header "Setting up Rust Plugin Marketplace for domain: $DOMAIN"

# Step 1: Create web directory
print_status "Creating web directory..."
mkdir -p $WEB_ROOT
chown -R www-data:www-data $WEB_ROOT
chmod -R 755 $WEB_ROOT

# Step 2: Create required directories
print_status "Creating required directories..."
mkdir -p $WEB_ROOT/logs
mkdir -p $WEB_ROOT/downloads
mkdir -p $WEB_ROOT/uploads
chmod -R 777 $WEB_ROOT/logs
chmod -R 777 $WEB_ROOT/downloads
chmod -R 755 $WEB_ROOT/uploads

# Step 3: Create Nginx configuration
print_status "Creating Nginx configuration..."
cat > $NGINX_CONFIG << EOF
server {
    listen 80;
    server_name $DOMAIN www.$DOMAIN;
    root $WEB_ROOT;
    index index.php index.html index.htm;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline' *.paypal.com *.stripe.com" always;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss application/json;

    # Rate limiting
    limit_req_zone \$binary_remote_addr zone=login:10m rate=5r/m;
    limit_req_zone \$binary_remote_addr zone=api:10m rate=10r/m;

    location / {
        try_files \$uri \$uri/ /index.php?\$query_string;
    }

    location ~ \.php$ {
        include snippets/fastcgi-php.conf;
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_param SCRIPT_FILENAME \$document_root\$fastcgi_script_name;
        include fastcgi_params;
        
        # Security for PHP files
        fastcgi_hide_header X-Powered-By;
    }

    # Protect sensitive files and directories
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    location ~ /(config|logs|admin|steamauth)/ {
        deny all;
        access_log off;
        log_not_found off;
    }

    location ~ \.(sql|conf|ini|log)$ {
        deny all;
        access_log off;
        log_not_found off;
    }

    # Rate limit login attempts
    location ~ /(login|steamauth) {
        limit_req zone=login burst=3 nodelay;
        try_files \$uri \$uri/ /index.php?\$query_string;
    }

    # Rate limit API calls
    location ~ /(payment|download|api) {
        limit_req zone=api burst=5 nodelay;
        try_files \$uri \$uri/ /index.php?\$query_string;
    }

    # Cache static files
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|woff|woff2|ttf|svg|webp)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # Secure downloads
    location /downloads/ {
        internal;
        alias $WEB_ROOT/downloads/;
    }

    # Limit file upload size
    client_max_body_size 50M;
    
    # Timeout settings
    fastcgi_read_timeout 300;
    proxy_read_timeout 300;
}
EOF

# Step 4: Enable site
print_status "Enabling Nginx site..."
ln -sf $NGINX_CONFIG /etc/nginx/sites-enabled/
nginx -t
systemctl reload nginx

# Step 5: Create database configuration template
print_status "Creating database configuration template..."
cat > $WEB_ROOT/database-config-template.php << 'EOF'
<?php
/**
 * Database Configuration Template
 * Copy this to database.php and update with your actual credentials
 */

$db_config = [
    'host' => 'localhost',
    'database' => 'plugin_marketplace',
    'username' => 'marketplace_user',
    'password' => 'YOUR_SECURE_PASSWORD_HERE',
    'charset' => 'utf8mb4'
];

try {
    $pdo = new PDO(
        "mysql:host={$db_config['host']};dbname={$db_config['database']};charset={$db_config['charset']}",
        $db_config['username'],
        $db_config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ]
    );
} catch (PDOException $e) {
    error_log('Database connection failed: ' . $e->getMessage());
    die('Database connection failed. Please check your configuration.');
}
?>
EOF

# Step 6: Create SSL setup script
print_status "Creating SSL setup script..."
cat > $WEB_ROOT/setup-ssl.sh << EOF
#!/bin/bash
# SSL Setup Script
# Run this after your domain is properly pointed to this server

echo "Setting up SSL certificate for $DOMAIN..."
certbot --nginx -d $DOMAIN -d www.$DOMAIN --non-interactive --agree-tos --email admin@$DOMAIN

# Add auto-renewal to crontab if not already present
(crontab -l 2>/dev/null; echo "0 12 * * * /usr/bin/certbot renew --quiet") | crontab -

echo "SSL certificate installed successfully!"
echo "Your site is now available at: https://$DOMAIN"
EOF

chmod +x $WEB_ROOT/setup-ssl.sh

# Step 7: Create backup script
print_status "Creating backup script..."
cat > /home/<USER>/backup-$DOMAIN.sh << EOF
#!/bin/bash
# Backup script for $DOMAIN

DATE=\$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/home/<USER>/backups/$DOMAIN"
SITE_DIR="$WEB_ROOT"

mkdir -p \$BACKUP_DIR

# Backup files
tar -czf \$BACKUP_DIR/site_\$DATE.tar.gz -C \$SITE_DIR .

# Backup database (uncomment and update credentials)
# mysqldump -u marketplace_user -p'your_password' plugin_marketplace > \$BACKUP_DIR/db_\$DATE.sql

# Keep only last 7 days of backups
find \$BACKUP_DIR -name "site_*.tar.gz" -mtime +7 -delete
find \$BACKUP_DIR -name "db_*.sql" -mtime +7 -delete

echo "Backup completed: \$BACKUP_DIR/site_\$DATE.tar.gz"
EOF

chmod +x /home/<USER>/backup-$DOMAIN.sh

# Step 8: Create monitoring script
print_status "Creating monitoring script..."
cat > /home/<USER>/monitor-$DOMAIN.sh << 'EOF'
#!/bin/bash
# Monitoring script

echo "=== System Status ==="
systemctl status nginx --no-pager -l
systemctl status php8.1-fpm --no-pager -l
systemctl status mysql --no-pager -l

echo -e "\n=== Disk Usage ==="
df -h

echo -e "\n=== Memory Usage ==="
free -h

echo -e "\n=== Recent Nginx Errors ==="
tail -n 10 /var/log/nginx/error.log

echo -e "\n=== Recent PHP Errors ==="
tail -n 10 /var/log/php8.1-fpm.log

echo -e "\n=== SSL Certificate Status ==="
certbot certificates
EOF

chmod +x /home/<USER>/monitor-$DOMAIN.sh

# Step 9: Create environment configuration
print_status "Creating environment configuration..."
cat > $WEB_ROOT/.env.example << EOF
# Environment Configuration
# Copy this to .env and update with your actual values

# Domain Configuration
DOMAIN=$DOMAIN
SITE_URL=https://$DOMAIN

# Database Configuration
DB_HOST=localhost
DB_NAME=plugin_marketplace
DB_USER=marketplace_user
DB_PASS=your_secure_password

# Payment Configuration
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_client_secret
PAYPAL_SANDBOX=false

STRIPE_PUBLISHABLE_KEY=pk_live_your_stripe_publishable_key
STRIPE_SECRET_KEY=sk_live_your_stripe_secret_key

CRYPTO_API_TOKEN=your_coingate_api_token
CRYPTO_SANDBOX=false

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
FROM_EMAIL=noreply@$DOMAIN
FROM_NAME=Plugin Marketplace

# Security
SESSION_SECRET=your_random_session_secret
DOWNLOAD_SECRET=your_random_download_secret
EOF

# Step 10: Set final permissions
print_status "Setting final permissions..."
chown -R www-data:www-data $WEB_ROOT
chmod -R 755 $WEB_ROOT
chmod -R 777 $WEB_ROOT/logs
chmod -R 777 $WEB_ROOT/downloads
chmod 600 $WEB_ROOT/.env.example

print_header "Setup completed successfully!"
echo ""
print_status "Next steps:"
echo "1. Upload your marketplace files to: $WEB_ROOT"
echo "2. Copy and configure: $WEB_ROOT/.env.example to $WEB_ROOT/.env"
echo "3. Copy and configure: $WEB_ROOT/database-config-template.php to $WEB_ROOT/database.php"
echo "4. Set up your database using the SQL in UBUNTU_DEPLOYMENT_GUIDE.md"
echo "5. Point your domain DNS to this server's IP address"
echo "6. Run SSL setup: $WEB_ROOT/setup-ssl.sh"
echo "7. Test your site: http://$DOMAIN"
echo ""
print_status "Useful commands:"
echo "- Monitor site: /home/<USER>/monitor-$DOMAIN.sh"
echo "- Backup site: /home/<USER>/backup-$DOMAIN.sh"
echo "- Check logs: tail -f $WEB_ROOT/logs/*.log"
echo ""
print_warning "Remember to:"
echo "- Update all placeholder values in configuration files"
echo "- Set up your payment provider accounts"
echo "- Configure your email settings"
echo "- Test all functionality before going live"
EOF
