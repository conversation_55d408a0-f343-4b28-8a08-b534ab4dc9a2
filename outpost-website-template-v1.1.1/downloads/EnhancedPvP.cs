/*
 * Enhanced PvP System Plugin for Rust
 * Version: 1.5.2
 * Author: CombatCoder
 * 
 * This is a sample plugin file for demonstration purposes.
 * In a real marketplace, this would be the actual plugin code.
 */

using System;
using System.Collections.Generic;
using System.Linq;
using Oxide.Core;
using Oxide.Core.Plugins;
using UnityEngine;

namespace Oxide.Plugins
{
    [Info("Enhanced PvP", "CombatCoder", "1.5.2")]
    [Description("Advanced PvP mechanics with kill streaks, bounties, and leaderboards")]
    public class EnhancedPvP : RustPlugin
    {
        #region Configuration
        
        private Configuration config;
        
        public class Configuration
        {
            public bool EnableKillStreaks { get; set; } = true;
            public bool EnableBounties { get; set; } = true;
            public bool EnableLeaderboard { get; set; } = true;
            public int MinBountyAmount { get; set; } = 100;
            public int MaxBountyAmount { get; set; } = 10000;
            public Dictionary<int, string> KillStreakRewards { get; set; } = new Dictionary<int, string>
            {
                { 3, "rifle.ak" },
                { 5, "ammo.rifle" },
                { 10, "explosive.timed" }
            };
        }
        
        protected override void LoadDefaultConfig()
        {
            config = new Configuration();
        }
        
        protected override void LoadConfig()
        {
            base.LoadConfig();
            config = Config.ReadObject<Configuration>();
        }
        
        protected override void SaveConfig()
        {
            Config.WriteObject(config);
        }
        
        #endregion
        
        #region Data Storage
        
        private Dictionary<ulong, PlayerStats> playerStats = new Dictionary<ulong, PlayerStats>();
        private Dictionary<ulong, BountyInfo> activeBounties = new Dictionary<ulong, BountyInfo>();
        
        public class PlayerStats
        {
            public int Kills { get; set; } = 0;
            public int Deaths { get; set; } = 0;
            public int KillStreak { get; set; } = 0;
            public int HighestKillStreak { get; set; } = 0;
            public DateTime LastKill { get; set; } = DateTime.MinValue;
        }
        
        public class BountyInfo
        {
            public ulong TargetId { get; set; }
            public ulong PlacedBy { get; set; }
            public int Amount { get; set; }
            public DateTime PlacedAt { get; set; }
            public string Reason { get; set; }
        }
        
        #endregion
        
        #region Hooks
        
        void Init()
        {
            LoadData();
            permission.RegisterPermission("enhancedpvp.use", this);
            permission.RegisterPermission("enhancedpvp.admin", this);
            permission.RegisterPermission("enhancedpvp.bounty", this);
        }
        
        void OnPlayerDeath(BasePlayer victim, HitInfo info)
        {
            if (victim == null) return;
            
            BasePlayer attacker = info?.InitiatorPlayer;
            if (attacker == null || attacker == victim) return;
            
            ProcessKill(attacker, victim);
        }
        
        void OnServerSave()
        {
            SaveData();
        }
        
        void Unload()
        {
            SaveData();
        }
        
        #endregion
        
        #region Commands
        
        [ChatCommand("pvpstats")]
        void PvpStatsCommand(BasePlayer player, string command, string[] args)
        {
            var stats = GetPlayerStats(player);
            float kdr = stats.Deaths > 0 ? (float)stats.Kills / stats.Deaths : stats.Kills;
            
            SendReply(player, $"PvP Stats:\nKills: {stats.Kills}\nDeaths: {stats.Deaths}\nK/D Ratio: {kdr:F2}\nCurrent Streak: {stats.KillStreak}\nBest Streak: {stats.HighestKillStreak}");
        }
        
        [ChatCommand("leaderboard")]
        void LeaderboardCommand(BasePlayer player, string command, string[] args)
        {
            if (!config.EnableLeaderboard)
            {
                SendReply(player, "Leaderboard is disabled.");
                return;
            }
            
            ShowLeaderboard(player);
        }
        
        [ChatCommand("bounty")]
        void BountyCommand(BasePlayer player, string command, string[] args)
        {
            if (!config.EnableBounties)
            {
                SendReply(player, "Bounty system is disabled.");
                return;
            }
            
            if (!permission.UserHasPermission(player.UserIDString, "enhancedpvp.bounty"))
            {
                SendReply(player, "You don't have permission to use bounties.");
                return;
            }
            
            if (args.Length < 2)
            {
                SendReply(player, "Usage: /bounty <player> <amount> [reason]");
                return;
            }
            
            BasePlayer target = BasePlayer.Find(args[0]);
            if (target == null)
            {
                SendReply(player, "Player not found.");
                return;
            }
            
            if (!int.TryParse(args[1], out int amount))
            {
                SendReply(player, "Invalid amount.");
                return;
            }
            
            string reason = args.Length > 2 ? string.Join(" ", args.Skip(2)) : "No reason given";
            PlaceBounty(player, target, amount, reason);
        }
        
        [ChatCommand("bounties")]
        void BountiesCommand(BasePlayer player, string command, string[] args)
        {
            if (!config.EnableBounties)
            {
                SendReply(player, "Bounty system is disabled.");
                return;
            }
            
            ShowActiveBounties(player);
        }
        
        #endregion
        
        #region Helper Methods
        
        private void LoadData()
        {
            try
            {
                playerStats = Interface.Oxide.DataFileSystem.ReadObject<Dictionary<ulong, PlayerStats>>("EnhancedPvP_Stats");
                activeBounties = Interface.Oxide.DataFileSystem.ReadObject<Dictionary<ulong, BountyInfo>>("EnhancedPvP_Bounties");
            }
            catch
            {
                playerStats = new Dictionary<ulong, PlayerStats>();
                activeBounties = new Dictionary<ulong, BountyInfo>();
            }
        }
        
        private void SaveData()
        {
            Interface.Oxide.DataFileSystem.WriteObject("EnhancedPvP_Stats", playerStats);
            Interface.Oxide.DataFileSystem.WriteObject("EnhancedPvP_Bounties", activeBounties);
        }
        
        private PlayerStats GetPlayerStats(BasePlayer player)
        {
            if (!playerStats.ContainsKey(player.userID))
                playerStats[player.userID] = new PlayerStats();
            
            return playerStats[player.userID];
        }
        
        private void ProcessKill(BasePlayer attacker, BasePlayer victim)
        {
            var attackerStats = GetPlayerStats(attacker);
            var victimStats = GetPlayerStats(victim);
            
            // Update stats
            attackerStats.Kills++;
            attackerStats.KillStreak++;
            attackerStats.LastKill = DateTime.Now;
            
            victimStats.Deaths++;
            victimStats.KillStreak = 0;
            
            // Update highest kill streak
            if (attackerStats.KillStreak > attackerStats.HighestKillStreak)
                attackerStats.HighestKillStreak = attackerStats.KillStreak;
            
            // Check for kill streak rewards
            if (config.EnableKillStreaks && config.KillStreakRewards.ContainsKey(attackerStats.KillStreak))
            {
                GiveKillStreakReward(attacker, attackerStats.KillStreak);
            }
            
            // Check for bounty completion
            if (config.EnableBounties && activeBounties.ContainsKey(victim.userID))
            {
                CompleteBounty(attacker, victim);
            }
            
            // Announce kill streak
            if (attackerStats.KillStreak >= 3)
            {
                Server.Broadcast($"{attacker.displayName} is on a {attackerStats.KillStreak} kill streak!");
            }
        }
        
        private void GiveKillStreakReward(BasePlayer player, int streak)
        {
            if (!config.KillStreakRewards.ContainsKey(streak)) return;
            
            string itemShortname = config.KillStreakRewards[streak];
            Item item = ItemManager.CreateByName(itemShortname, 1);
            
            if (item != null)
            {
                player.GiveItem(item);
                SendReply(player, $"Kill streak reward: {item.info.displayName.english}!");
            }
        }
        
        private void PlaceBounty(BasePlayer placer, BasePlayer target, int amount, string reason)
        {
            if (amount < config.MinBountyAmount || amount > config.MaxBountyAmount)
            {
                SendReply(placer, $"Bounty amount must be between {config.MinBountyAmount} and {config.MaxBountyAmount}.");
                return;
            }
            
            if (activeBounties.ContainsKey(target.userID))
            {
                SendReply(placer, "This player already has a bounty on them.");
                return;
            }
            
            // In a real implementation, you would deduct currency from the placer
            
            var bounty = new BountyInfo
            {
                TargetId = target.userID,
                PlacedBy = placer.userID,
                Amount = amount,
                PlacedAt = DateTime.Now,
                Reason = reason
            };
            
            activeBounties[target.userID] = bounty;
            
            Server.Broadcast($"A bounty of {amount} has been placed on {target.displayName}! Reason: {reason}");
            SendReply(placer, $"Bounty placed on {target.displayName} for {amount}.");
        }
        
        private void CompleteBounty(BasePlayer hunter, BasePlayer target)
        {
            if (!activeBounties.ContainsKey(target.userID)) return;
            
            var bounty = activeBounties[target.userID];
            activeBounties.Remove(target.userID);
            
            // In a real implementation, you would give currency to the hunter
            
            Server.Broadcast($"{hunter.displayName} has claimed the bounty on {target.displayName} for {bounty.Amount}!");
            SendReply(hunter, $"You claimed a bounty worth {bounty.Amount}!");
        }
        
        private void ShowLeaderboard(BasePlayer player)
        {
            var topKillers = playerStats
                .OrderByDescending(x => x.Value.Kills)
                .Take(10)
                .ToList();
            
            string leaderboard = "Top 10 Killers:\n";
            for (int i = 0; i < topKillers.Count; i++)
            {
                var entry = topKillers[i];
                string playerName = BasePlayer.FindByID(entry.Key)?.displayName ?? "Unknown";
                leaderboard += $"{i + 1}. {playerName}: {entry.Value.Kills} kills\n";
            }
            
            SendReply(player, leaderboard);
        }
        
        private void ShowActiveBounties(BasePlayer player)
        {
            if (activeBounties.Count == 0)
            {
                SendReply(player, "No active bounties.");
                return;
            }
            
            string bountyList = "Active Bounties:\n";
            foreach (var bounty in activeBounties.Values)
            {
                string targetName = BasePlayer.FindByID(bounty.TargetId)?.displayName ?? "Unknown";
                bountyList += $"{targetName}: {bounty.Amount} - {bounty.Reason}\n";
            }
            
            SendReply(player, bountyList);
        }
        
        #endregion
    }
}
