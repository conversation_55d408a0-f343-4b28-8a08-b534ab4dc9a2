/*
 * Advanced Teleportation Plugin for Rust
 * Version: 2.1.0
 * Author: PluginDev
 * 
 * This is a sample plugin file for demonstration purposes.
 * In a real marketplace, this would be the actual plugin code.
 */

using System;
using System.Collections.Generic;
using Oxide.Core;
using Oxide.Core.Plugins;
using UnityEngine;

namespace Oxide.Plugins
{
    [Info("Advanced Teleportation", "PluginDev", "2.1.0")]
    [Description("Comprehensive teleportation system with homes, warps, and admin teleports")]
    public class AdvancedTeleportation : RustPlugin
    {
        #region Configuration
        
        private Configuration config;
        
        public class Configuration
        {
            public int MaxHomes { get; set; } = 5;
            public int TeleportCooldown { get; set; } = 300;
            public bool AllowTeleportInCombat { get; set; } = false;
            public float TeleportDelay { get; set; } = 5.0f;
        }
        
        protected override void LoadDefaultConfig()
        {
            config = new Configuration();
        }
        
        protected override void LoadConfig()
        {
            base.LoadConfig();
            config = Config.ReadObject<Configuration>();
        }
        
        protected override void SaveConfig()
        {
            Config.WriteObject(config);
        }
        
        #endregion
        
        #region Data Storage
        
        private Dictionary<ulong, PlayerData> playerData = new Dictionary<ulong, PlayerData>();
        
        public class PlayerData
        {
            public Dictionary<string, Vector3> Homes { get; set; } = new Dictionary<string, Vector3>();
            public DateTime LastTeleport { get; set; } = DateTime.MinValue;
        }
        
        #endregion
        
        #region Hooks
        
        void Init()
        {
            LoadData();
            permission.RegisterPermission("advancedtp.use", this);
            permission.RegisterPermission("advancedtp.admin", this);
        }
        
        void OnServerSave()
        {
            SaveData();
        }
        
        void Unload()
        {
            SaveData();
        }
        
        #endregion
        
        #region Commands
        
        [ChatCommand("home")]
        void HomeCommand(BasePlayer player, string command, string[] args)
        {
            if (!permission.UserHasPermission(player.UserIDString, "advancedtp.use"))
            {
                SendReply(player, "You don't have permission to use this command.");
                return;
            }
            
            if (args.Length == 0)
            {
                ListHomes(player);
                return;
            }
            
            string homeName = args[0].ToLower();
            TeleportToHome(player, homeName);
        }
        
        [ChatCommand("sethome")]
        void SetHomeCommand(BasePlayer player, string command, string[] args)
        {
            if (!permission.UserHasPermission(player.UserIDString, "advancedtp.use"))
            {
                SendReply(player, "You don't have permission to use this command.");
                return;
            }
            
            string homeName = args.Length > 0 ? args[0].ToLower() : "home";
            SetHome(player, homeName);
        }
        
        [ChatCommand("delhome")]
        void DelHomeCommand(BasePlayer player, string command, string[] args)
        {
            if (!permission.UserHasPermission(player.UserIDString, "advancedtp.use"))
            {
                SendReply(player, "You don't have permission to use this command.");
                return;
            }
            
            if (args.Length == 0)
            {
                SendReply(player, "Usage: /delhome <name>");
                return;
            }
            
            string homeName = args[0].ToLower();
            DeleteHome(player, homeName);
        }
        
        [ChatCommand("tpa")]
        void TpaCommand(BasePlayer player, string command, string[] args)
        {
            if (!permission.UserHasPermission(player.UserIDString, "advancedtp.use"))
            {
                SendReply(player, "You don't have permission to use this command.");
                return;
            }
            
            if (args.Length == 0)
            {
                SendReply(player, "Usage: /tpa <player>");
                return;
            }
            
            // Implementation for teleport requests would go here
            SendReply(player, "Teleport request functionality coming soon!");
        }
        
        #endregion
        
        #region Helper Methods
        
        private void LoadData()
        {
            try
            {
                playerData = Interface.Oxide.DataFileSystem.ReadObject<Dictionary<ulong, PlayerData>>("AdvancedTeleportation");
            }
            catch
            {
                playerData = new Dictionary<ulong, PlayerData>();
            }
        }
        
        private void SaveData()
        {
            Interface.Oxide.DataFileSystem.WriteObject("AdvancedTeleportation", playerData);
        }
        
        private PlayerData GetPlayerData(BasePlayer player)
        {
            if (!playerData.ContainsKey(player.userID))
                playerData[player.userID] = new PlayerData();
            
            return playerData[player.userID];
        }
        
        private void SetHome(BasePlayer player, string homeName)
        {
            var data = GetPlayerData(player);
            
            if (data.Homes.Count >= config.MaxHomes && !data.Homes.ContainsKey(homeName))
            {
                SendReply(player, $"You can only have {config.MaxHomes} homes maximum.");
                return;
            }
            
            data.Homes[homeName] = player.transform.position;
            SendReply(player, $"Home '{homeName}' has been set!");
        }
        
        private void TeleportToHome(BasePlayer player, string homeName)
        {
            var data = GetPlayerData(player);
            
            if (!data.Homes.ContainsKey(homeName))
            {
                SendReply(player, $"Home '{homeName}' not found.");
                return;
            }
            
            if (!CanTeleport(player))
                return;
            
            Vector3 homePosition = data.Homes[homeName];
            
            timer.Once(config.TeleportDelay, () =>
            {
                if (player == null || !player.IsConnected) return;
                
                player.Teleport(homePosition);
                SendReply(player, $"Teleported to home '{homeName}'!");
                data.LastTeleport = DateTime.Now;
            });
            
            SendReply(player, $"Teleporting to '{homeName}' in {config.TeleportDelay} seconds...");
        }
        
        private void ListHomes(BasePlayer player)
        {
            var data = GetPlayerData(player);
            
            if (data.Homes.Count == 0)
            {
                SendReply(player, "You have no homes set. Use /sethome <name> to set one.");
                return;
            }
            
            string homesList = string.Join(", ", data.Homes.Keys);
            SendReply(player, $"Your homes: {homesList}");
        }
        
        private void DeleteHome(BasePlayer player, string homeName)
        {
            var data = GetPlayerData(player);
            
            if (!data.Homes.ContainsKey(homeName))
            {
                SendReply(player, $"Home '{homeName}' not found.");
                return;
            }
            
            data.Homes.Remove(homeName);
            SendReply(player, $"Home '{homeName}' has been deleted.");
        }
        
        private bool CanTeleport(BasePlayer player)
        {
            var data = GetPlayerData(player);
            
            // Check cooldown
            if ((DateTime.Now - data.LastTeleport).TotalSeconds < config.TeleportCooldown)
            {
                int remainingTime = config.TeleportCooldown - (int)(DateTime.Now - data.LastTeleport).TotalSeconds;
                SendReply(player, $"You must wait {remainingTime} seconds before teleporting again.");
                return false;
            }
            
            // Check combat (simplified)
            if (!config.AllowTeleportInCombat && player.IsWounded())
            {
                SendReply(player, "You cannot teleport while wounded.");
                return false;
            }
            
            return true;
        }
        
        #endregion
    }
}
