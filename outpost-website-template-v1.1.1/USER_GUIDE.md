# Plugin Marketplace User Guide

Welcome to your Rust Plugin Marketplace! This guide will help you understand how to browse, purchase, and download plugins.

## 🎮 Getting Started

### Creating an Account

1. **Steam Login Required**
   - Click the "Login with Steam" button
   - You'll be redirected to Steam to authorize
   - After authorization, you'll be logged into the marketplace

2. **Account Benefits**
   - Track your purchased plugins
   - Access download history
   - Manage your licenses
   - Get support for your purchases

## 🔍 Browsing Plugins

### Plugin Categories

Our marketplace organizes plugins into these categories:

- **🔫 PvP**: Player vs Player combat enhancements
- **🏰 PvE**: Player vs Environment content
- **⚙️ Admin Tools**: Server administration utilities
- **💰 Economy**: Economic system plugins
- **🎉 Fun**: Entertainment and social features
- **🔧 Utility**: General utility and helper tools

### Search and Filtering

1. **Search Bar**
   - Type plugin names or keywords
   - Search results update automatically
   - Use specific terms for better results

2. **Category Filter**
   - Select "All Categories" or specific category
   - Filters update the plugin list instantly

3. **Sorting Options**
   - **Name (A-Z)**: Alphabetical order
   - **Price (Low to High)**: Cheapest first
   - **Price (High to Low)**: Most expensive first
   - **Newest**: Recently added plugins first

4. **Clear Filters**
   - Click "Clear Filters" to reset all options
   - Returns to showing all available plugins

## 🛒 Purchasing Plugins

### Plugin Information

Each plugin card shows:
- **Plugin Name** and version
- **Author** information
- **Price** in USD
- **Category** classification
- **Short Description**
- **Preview Image**

### Detailed Plugin View

Click "View Details" to see:
- Full description
- Feature list
- System requirements
- Version history/changelog
- Author information
- Customer reviews (if available)

### Purchase Process

1. **Select Payment Method**
   - PayPal (recommended)
   - Credit/Debit Card (via Stripe)
   - Cryptocurrency (if enabled)

2. **Complete Payment**
   - You'll be redirected to the payment provider
   - Complete the transaction securely
   - Return to the marketplace automatically

3. **Download Access**
   - Immediate access after successful payment
   - Download link sent to your email (if configured)
   - Access via "My Licenses" page

## 📥 Downloading Plugins

### Download Process

1. **Access Your Licenses**
   - Click "My Licenses" in the navigation
   - View all your purchased plugins
   - See download status and limits

2. **Download Plugin**
   - Click "Download" button next to your plugin
   - File downloads automatically
   - Each license allows up to 5 downloads

3. **License Key**
   - Each download includes a unique license key
   - License key is embedded in the plugin file
   - Keep your license key for support requests

### Download Limits

- **Maximum Downloads**: 5 per license
- **Download Expiry**: Links expire after 24 hours
- **Re-download**: Generate new download links anytime
- **License Validity**: Lifetime access to purchased plugins

## 🔑 License Management

### My Licenses Page

View and manage all your plugin licenses:

- **Plugin Information**: Name, version, author
- **License Key**: Unique identifier for your copy
- **Purchase Date**: When you bought the plugin
- **Download Count**: How many times you've downloaded
- **Status**: Active, expired, or revoked
- **Actions**: Download, view details, get support

### License Details

Click "Details" for comprehensive information:
- Complete plugin information
- License key and purchase details
- Download history with timestamps
- Support contact information

### License Key Format

License keys follow this format: `ABCD-1234-EFGH-5678`
- Keep your license key safe
- Required for technical support
- Proves legitimate ownership
- Embedded in downloaded files

## 🔧 Installing Plugins

### Server Installation

1. **Upload to Server**
   ```bash
   # Upload the .cs file to your server's plugins directory
   # Usually: /oxide/plugins/ or similar
   ```

2. **Load Plugin**
   ```
   # In server console or RCON:
   oxide.load PluginName
   ```

3. **Configure Plugin**
   - Check for configuration files
   - Modify settings as needed
   - Restart if required

### Plugin Requirements

Check each plugin for:
- **Oxide Version**: Minimum required version
- **Rust Version**: Compatible game versions
- **Dependencies**: Other required plugins
- **Permissions**: Required server permissions

## 💡 Tips and Best Practices

### Before Purchasing

1. **Read Descriptions Carefully**
   - Understand what the plugin does
   - Check compatibility requirements
   - Review feature lists

2. **Check Reviews** (if available)
   - See what other users say
   - Look for common issues
   - Verify plugin quality

3. **Consider Your Needs**
   - Will this plugin benefit your server?
   - Do you have the technical skills to configure it?
   - Is it worth the price?

### After Purchasing

1. **Download Immediately**
   - Don't wait to download your plugins
   - Save files in a secure location
   - Keep license keys recorded

2. **Test on Development Server**
   - Test plugins before production use
   - Check for conflicts with existing plugins
   - Verify functionality

3. **Keep Backups**
   - Save plugin files safely
   - Document your configurations
   - Keep license information

## 🆘 Getting Support

### Self-Help Resources

1. **Plugin Documentation**
   - Check plugin descriptions for usage info
   - Look for configuration examples
   - Review changelog for updates

2. **Community Forums**
   - Rust server admin communities
   - Plugin-specific discussion threads
   - Share experiences with other users

### Contacting Support

When you need help:

1. **Gather Information**
   - Plugin name and version
   - Your license key
   - Server details (Oxide version, etc.)
   - Error messages or logs

2. **Contact Methods**
   - Email support (check plugin details)
   - Support ticket system (if available)
   - Community forums

3. **What to Include**
   - Clear description of the problem
   - Steps to reproduce the issue
   - Server logs or error messages
   - Your license key for verification

## ⚠️ Important Notes

### License Terms

- **Personal Use**: Licenses are for your servers only
- **No Redistribution**: Don't share plugin files
- **No Resale**: Cannot resell purchased plugins
- **Support Included**: Get help from plugin authors

### Security

- **Secure Downloads**: All downloads are encrypted
- **Safe Payments**: Payments processed securely
- **Privacy Protected**: Your information is safe
- **Legitimate Sources**: Only buy from official marketplace

### Refund Policy

- **Review Before Purchase**: Sales are generally final
- **Technical Issues**: Support available for problems
- **Defective Plugins**: Refunds for non-functional plugins
- **Contact Support**: Discuss issues before requesting refunds

## 📞 Contact Information

Need help with the marketplace itself?

- **Technical Issues**: Contact server administrator
- **Payment Problems**: Check with payment provider
- **Plugin Support**: Contact plugin authors directly
- **Account Issues**: Use marketplace support system

## 🎯 Frequently Asked Questions

**Q: How many times can I download a plugin?**
A: Each license allows up to 5 downloads.

**Q: Do plugins work on all Rust servers?**
A: Plugins require Oxide/uMod framework. Check requirements.

**Q: Can I use plugins on multiple servers?**
A: Licenses are typically for single server use. Check terms.

**Q: What if a plugin doesn't work?**
A: Contact the plugin author for support using your license key.

**Q: Are updates included?**
A: Update policies vary by plugin. Check with the author.

**Q: Can I get a refund?**
A: Refund policies vary. Contact support for assistance.

**Q: Is my payment information secure?**
A: Yes, all payments are processed through secure, encrypted systems.

**Q: How do I install plugins?**
A: Upload the .cs file to your server's plugins directory and load it.

Enjoy your plugin marketplace experience! 🚀
