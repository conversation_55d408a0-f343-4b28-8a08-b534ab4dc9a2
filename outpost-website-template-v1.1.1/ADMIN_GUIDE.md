# Plugin Marketplace Admin Guide

This guide covers how to manage your Rust Plugin Marketplace, add new plugins, and maintain the system.

## 📋 Table of Contents

1. [Adding New Plugins](#adding-new-plugins)
2. [Managing Existing Plugins](#managing-existing-plugins)
3. [Payment Management](#payment-management)
4. [User Management](#user-management)
5. [System Maintenance](#system-maintenance)
6. [Troubleshooting](#troubleshooting)

## 🔧 Adding New Plugins

### Step 1: Prepare Plugin Files

1. **Upload Plugin File**
   ```bash
   # Upload your .cs plugin file to the downloads directory
   scp your-plugin.cs user@server:/var/www/your-domain.com/downloads/
   
   # Set proper permissions
   sudo chown www-data:www-data /var/www/your-domain.com/downloads/your-plugin.cs
   sudo chmod 644 /var/www/your-domain.com/downloads/your-plugin.cs
   ```

2. **Add Plugin Image**
   ```bash
   # Upload plugin preview image (recommended: 300x200px)
   scp plugin-image.jpg user@server:/var/www/your-domain.com/assets/img/
   ```

### Step 2: Update Configuration

Edit `/var/www/your-domain.com/config.php` and add your plugin to the `plugins` array:

```php
'plugins' => [
    // Existing plugins...
    
    [
        'id' => 6, // Use next available ID
        'name' => 'Your Plugin Name',
        'description' => 'Detailed description of what your plugin does...',
        'short_description' => 'Brief one-line description',
        'price' => 15.00,
        'currency' => 'USD',
        'image' => 'your-plugin-image.jpg',
        'file' => 'your-plugin.cs',
        'version' => '1.0.0',
        'author' => 'Plugin Author',
        'category' => 'Admin Tools', // PvP, PvE, Admin Tools, Economy, Fun, Utility
        'featured' => false,
        'enabled' => true,
        'date_added' => '2024-06-24', // Today's date
        'requirements' => [
            'oxide_version' => '2.0.0',
            'rust_version' => 'Any',
            'dependencies' => [] // Other required plugins
        ],
        'features' => [
            'Feature 1',
            'Feature 2',
            'Feature 3'
        ],
        'changelog' => [
            '1.0.0' => 'Initial release'
        ]
    ]
]
```

### Step 3: Test the Plugin

1. **Check Plugin Display**
   - Visit your marketplace
   - Verify the plugin appears in the correct category
   - Test filtering and search functionality

2. **Test Purchase Flow**
   - Use sandbox/test payment methods
   - Verify download links work
   - Check license key generation

## 📝 Managing Existing Plugins

### Updating Plugin Information

To update plugin details, edit the plugin entry in `config.php`:

```php
// Example: Update price and version
[
    'id' => 1,
    'name' => 'Advanced Teleportation',
    'price' => 20.00, // Updated price
    'version' => '2.2.0', // New version
    'changelog' => [
        '2.2.0' => 'Added new teleport effects and bug fixes',
        '2.1.0' => 'Performance improvements',
        '2.0.0' => 'Major rewrite with new features'
    ]
    // ... other fields
]
```

### Disabling/Enabling Plugins

```php
// Temporarily disable a plugin
'enabled' => false,

// Re-enable a plugin
'enabled' => true,
```

### Featuring Plugins

```php
// Feature a plugin (shows in featured section)
'featured' => true,

// Remove from featured
'featured' => false,
```

### Updating Plugin Files

1. **Upload New Version**
   ```bash
   # Backup old version
   cp /var/www/your-domain.com/downloads/plugin.cs /var/www/your-domain.com/downloads/plugin-v1.0.cs
   
   # Upload new version
   scp new-plugin.cs user@server:/var/www/your-domain.com/downloads/plugin.cs
   ```

2. **Update Version in Config**
   ```php
   'version' => '2.0.0',
   'changelog' => [
       '2.0.0' => 'Major update with new features',
       '1.0.0' => 'Initial release'
   ]
   ```

## 💳 Payment Management

### Monitoring Sales

Check payment logs:
```bash
# View recent purchases
tail -f /var/www/your-domain.com/logs/purchases.log

# View download activity
tail -f /var/www/your-domain.com/logs/downloads.log
```

### Payment Provider Management

#### PayPal Configuration
```php
'paypal' => [
    'enabled' => true,
    'sandbox' => false, // Set to true for testing
    'client_id' => 'YOUR_PAYPAL_CLIENT_ID',
    'client_secret' => 'YOUR_PAYPAL_CLIENT_SECRET'
]
```

#### Stripe Configuration
```php
'stripe' => [
    'enabled' => true,
    'publishable_key' => 'pk_live_YOUR_STRIPE_PUBLISHABLE_KEY',
    'secret_key' => 'sk_live_YOUR_STRIPE_SECRET_KEY'
]
```

### Handling Refunds

1. **Process Refund in Payment Provider**
   - Log into PayPal/Stripe dashboard
   - Find the transaction
   - Process refund

2. **Revoke License** (if using database)
   ```sql
   UPDATE licenses 
   SET status = 'revoked' 
   WHERE license_key = 'XXXX-XXXX-XXXX-XXXX';
   ```

## 👥 User Management

### Viewing User Activity

```bash
# Check user login logs
grep "steam_login" /var/log/nginx/access.log

# View download activity by user
grep "user_steam_id" /var/www/your-domain.com/logs/downloads.log
```

### Managing Problem Users

1. **Block User Downloads** (if using database)
   ```sql
   UPDATE licenses 
   SET status = 'revoked' 
   WHERE user_steam_id = 'STEAM_ID_HERE';
   ```

2. **IP Blocking** (via Nginx)
   ```bash
   # Add to Nginx config
   deny *************;
   ```

## 🔧 System Maintenance

### Regular Maintenance Tasks

#### Daily
```bash
# Check system status
/home/<USER>/monitor-your-domain.sh

# Check disk space
df -h

# Review error logs
tail -n 50 /var/log/nginx/error.log
tail -n 50 /var/log/php8.1-fpm.log
```

#### Weekly
```bash
# Create backup
/home/<USER>/backup-your-domain.sh

# Update system packages
sudo apt update && sudo apt upgrade -y

# Clean old logs
sudo find /var/log -name "*.log" -mtime +30 -delete
```

#### Monthly
```bash
# Review SSL certificate
sudo certbot certificates

# Check security updates
sudo unattended-upgrades --dry-run

# Review payment provider statements
# Check for any discrepancies
```

### Performance Optimization

#### Enable OPcache
```bash
sudo nano /etc/php/8.1/fpm/conf.d/10-opcache.ini
```

```ini
opcache.enable=1
opcache.memory_consumption=128
opcache.max_accelerated_files=4000
opcache.revalidate_freq=60
opcache.validate_timestamps=1
```

#### Nginx Optimization
```nginx
# Add to your Nginx config
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

# Browser caching
location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

### Security Maintenance

#### Update Dependencies
```bash
# Update Composer packages (if using)
cd /var/www/your-domain.com
sudo -u www-data composer update

# Check for security updates
sudo apt list --upgradable
```

#### Monitor Failed Login Attempts
```bash
# Check fail2ban status
sudo fail2ban-client status

# View banned IPs
sudo fail2ban-client status nginx-http-auth
```

#### Review File Permissions
```bash
# Ensure proper permissions
sudo chown -R www-data:www-data /var/www/your-domain.com
sudo chmod -R 755 /var/www/your-domain.com
sudo chmod -R 777 /var/www/your-domain.com/logs
sudo chmod -R 777 /var/www/your-domain.com/downloads
```

## 🚨 Troubleshooting

### Common Issues

#### Plugin Not Showing
1. **Check Configuration**
   ```php
   // Ensure plugin is enabled
   'enabled' => true,
   
   // Check for syntax errors in config.php
   php -l /var/www/your-domain.com/config.php
   ```

2. **Clear Cache** (if using caching)
   ```bash
   # Clear OPcache
   sudo systemctl restart php8.1-fpm
   ```

#### Download Issues
1. **Check File Permissions**
   ```bash
   ls -la /var/www/your-domain.com/downloads/
   # Should show: -rw-r--r-- www-data www-data
   ```

2. **Verify Download Tokens**
   ```bash
   # Check token generation logs
   grep "download_token" /var/www/your-domain.com/logs/downloads.log
   ```

#### Payment Problems
1. **Check API Credentials**
   - Verify sandbox vs production mode
   - Test API keys in provider dashboard

2. **Review Payment Logs**
   ```bash
   tail -f /var/www/your-domain.com/logs/payments.log
   ```

#### Performance Issues
1. **Check Resource Usage**
   ```bash
   htop
   iotop
   ```

2. **Review Slow Queries** (if using database)
   ```bash
   sudo tail -f /var/log/mysql/slow.log
   ```

### Emergency Procedures

#### Site Down
1. **Check Services**
   ```bash
   sudo systemctl status nginx
   sudo systemctl status php8.1-fpm
   sudo systemctl status mysql
   ```

2. **Restart Services**
   ```bash
   sudo systemctl restart nginx
   sudo systemctl restart php8.1-fpm
   ```

#### Database Issues
1. **Check Database Connection**
   ```bash
   mysql -u marketplace_user -p plugin_marketplace
   ```

2. **Restore from Backup**
   ```bash
   # Find latest backup
   ls -la /home/<USER>/backups/your-domain/
   
   # Restore database
   mysql -u marketplace_user -p plugin_marketplace < backup_file.sql
   ```

### Getting Help

1. **Check Documentation**
   - Review this guide
   - Check `UBUNTU_DEPLOYMENT_GUIDE.md`
   - Review `QUICK_START_GUIDE.md`

2. **Log Analysis**
   - Always check logs first
   - Look for error patterns
   - Note timestamps of issues

3. **Community Support**
   - Rust server admin communities
   - Web development forums
   - Payment provider support

## 📊 Analytics and Reporting

### Sales Reporting
Create custom scripts to analyze your sales data:

```bash
# Daily sales summary
grep "$(date +%Y-%m-%d)" /var/www/your-domain.com/logs/purchases.log | wc -l

# Popular plugins
grep "plugin_id" /var/www/your-domain.com/logs/purchases.log | sort | uniq -c | sort -nr
```

### User Analytics
```bash
# Unique users today
grep "$(date +%Y-%m-%d)" /var/www/your-domain.com/logs/downloads.log | cut -d'"' -f4 | sort | uniq | wc -l

# Download patterns
grep "download" /var/www/your-domain.com/logs/downloads.log | cut -d' ' -f1,2 | uniq -c
```

Remember to regularly backup your data and keep your system updated for optimal security and performance!
