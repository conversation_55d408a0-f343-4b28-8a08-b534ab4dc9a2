<?php
session_start();

/**
 * download-enhanced.php
 * Enhanced Secure Plugin Download Handler with License Key Generation
 * @owner     Plugin Marketplace
 * @copyright 2024 Plugin Marketplace
 */

require_once __DIR__ . '/plugin-core.php';

// Check if user is logged in
if (!isset($_SESSION['steamid'])) {
    http_response_code(403);
    die('Access denied. Please log in.');
}

$token = $_GET['token'] ?? '';
$pluginId = $_GET['plugin'] ?? '';

if (empty($token) || empty($pluginId)) {
    http_response_code(400);
    die('Invalid download request.');
}

// Verify download token and check expiration
$tokenData = verifyDownloadToken($token, $pluginId, $_SESSION['steamid']);
if (!$tokenData['valid']) {
    http_response_code(403);
    die($tokenData['error'] ?? 'Invalid or expired download token.');
}

// Get plugin information
$plugin = getPluginById($config, $pluginId);
if (!$plugin) {
    http_response_code(404);
    die('Plugin not found.');
}

$filePath = __DIR__ . '/downloads/' . $plugin['file'];

if (!file_exists($filePath)) {
    http_response_code(404);
    die('Plugin file not found.');
}

// Check download limits
$downloadInfo = getDownloadInfo($token);
if ($downloadInfo['count'] >= $downloadInfo['max_downloads']) {
    http_response_code(403);
    die('Download limit exceeded. Maximum ' . $downloadInfo['max_downloads'] . ' downloads allowed.');
}

// Generate license key for this download
$licenseKey = generateLicenseKey($plugin, $_SESSION['steamid']);

// Create a temporary file with license information embedded
$tempFile = createLicensedPluginFile($filePath, $plugin, $licenseKey, $_SESSION['steamid']);

// Update download count
updateDownloadCount($token);

// Log the download
logDownload($_SESSION['steamid'], $pluginId, $plugin['name'], $licenseKey);

// Set headers for file download
header('Content-Type: application/octet-stream');
header('Content-Disposition: attachment; filename="' . basename($plugin['file']) . '"');
header('Content-Length: ' . filesize($tempFile));
header('Cache-Control: no-cache, must-revalidate');
header('Expires: 0');
header('X-License-Key: ' . $licenseKey);

// Output the file
readfile($tempFile);

// Clean up temporary file
unlink($tempFile);
exit;

/**
 * Verify download token with expiration and usage limits
 */
function verifyDownloadToken($token, $pluginId, $steamId) {
    // In a real implementation, this would check a database
    // For demo purposes, we'll decode the token and verify its components
    
    $tokenParts = explode('_', $token);
    if (count($tokenParts) !== 4) {
        return ['valid' => false, 'error' => 'Invalid token format'];
    }
    
    list($prefix, $pluginHash, $userHash, $timestamp) = $tokenParts;
    
    if ($prefix !== 'DL') {
        return ['valid' => false, 'error' => 'Invalid token prefix'];
    }
    
    // Check if token has expired (24 hours)
    $tokenTime = (int)$timestamp;
    $currentTime = time();
    $expiryTime = 24 * 60 * 60; // 24 hours
    
    if (($currentTime - $tokenTime) > $expiryTime) {
        return ['valid' => false, 'error' => 'Download token has expired'];
    }
    
    // Verify plugin and user hashes
    $expectedPluginHash = substr(md5($pluginId . 'plugin_salt'), 0, 8);
    $expectedUserHash = substr(md5($steamId . 'user_salt'), 0, 8);
    
    if ($pluginHash !== $expectedPluginHash || $userHash !== $expectedUserHash) {
        return ['valid' => false, 'error' => 'Token validation failed'];
    }
    
    return ['valid' => true];
}

/**
 * Get download information (count, limits, etc.)
 */
function getDownloadInfo($token) {
    // In a real implementation, this would query a database
    // For demo purposes, we'll return default values
    return [
        'count' => 0,
        'max_downloads' => 5,
        'expires_at' => date('Y-m-d H:i:s', time() + (30 * 24 * 60 * 60)) // 30 days
    ];
}

/**
 * Generate a unique license key for the plugin
 */
function generateLicenseKey($plugin, $steamId) {
    $data = $plugin['id'] . $plugin['name'] . $steamId . time();
    $hash = hash('sha256', $data);
    
    // Format as a readable license key: XXXX-XXXX-XXXX-XXXX
    $key = strtoupper(substr($hash, 0, 16));
    return substr($key, 0, 4) . '-' . substr($key, 4, 4) . '-' . substr($key, 8, 4) . '-' . substr($key, 12, 4);
}

/**
 * Create a temporary plugin file with license information embedded
 */
function createLicensedPluginFile($originalFile, $plugin, $licenseKey, $steamId) {
    $content = file_get_contents($originalFile);
    
    // Add license header to the plugin file
    $licenseHeader = "/*\n";
    $licenseHeader .= " * Licensed to: Steam ID " . $steamId . "\n";
    $licenseHeader .= " * License Key: " . $licenseKey . "\n";
    $licenseHeader .= " * Plugin: " . $plugin['name'] . " v" . $plugin['version'] . "\n";
    $licenseHeader .= " * Licensed on: " . date('Y-m-d H:i:s') . "\n";
    $licenseHeader .= " * \n";
    $licenseHeader .= " * This plugin is licensed for use on servers owned by the above Steam ID.\n";
    $licenseHeader .= " * Redistribution or sharing of this plugin is prohibited.\n";
    $licenseHeader .= " * For support, contact: " . ($GLOBALS['config']['store']['supportEmail'] ?? '<EMAIL>') . "\n";
    $licenseHeader .= " */\n\n";
    
    // Insert license header after any existing using statements
    if (strpos($content, 'using ') !== false) {
        $lines = explode("\n", $content);
        $insertIndex = 0;
        
        // Find the last 'using' statement
        for ($i = 0; $i < count($lines); $i++) {
            if (strpos(trim($lines[$i]), 'using ') === 0) {
                $insertIndex = $i + 1;
            }
        }
        
        array_splice($lines, $insertIndex, 0, explode("\n", $licenseHeader));
        $content = implode("\n", $lines);
    } else {
        $content = $licenseHeader . $content;
    }
    
    // Create temporary file
    $tempFile = tempnam(sys_get_temp_dir(), 'plugin_');
    file_put_contents($tempFile, $content);
    
    return $tempFile;
}

/**
 * Update download count for a token
 */
function updateDownloadCount($token) {
    // In a real implementation, this would update a database
    // For demo purposes, we'll just log it
    $logEntry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'token' => $token,
        'action' => 'download_count_updated'
    ];
    
    $logFile = 'logs/download_counts.log';
    if (!file_exists('logs')) {
        mkdir('logs', 0755, true);
    }
    file_put_contents($logFile, json_encode($logEntry) . "\n", FILE_APPEND | LOCK_EX);
}

/**
 * Log download activity
 */
function logDownload($steamId, $pluginId, $pluginName, $licenseKey) {
    $logEntry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'user_steam_id' => $steamId,
        'plugin_id' => $pluginId,
        'plugin_name' => $pluginName,
        'license_key' => $licenseKey,
        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
    ];

    $logFile = 'logs/downloads.log';
    if (!file_exists('logs')) {
        mkdir('logs', 0755, true);
    }
    file_put_contents($logFile, json_encode($logEntry) . "\n", FILE_APPEND | LOCK_EX);
}

?>
