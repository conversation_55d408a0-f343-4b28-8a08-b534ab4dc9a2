<?php
session_start();

/**
 * download.php
 * Secure Plugin Download Handler
 * @owner     Plugin Marketplace
 * @copyright 2024 Plugin Marketplace
 */

require_once __DIR__ . '/plugin-core.php';

// Check if user is logged in (you might want to implement a purchase verification system)
if (!isset($_SESSION['steamid'])) {
    http_response_code(401);
    die('You must be logged in to download plugins.');
}

// Get download token and plugin ID
$token = $_GET['token'] ?? '';
$pluginId = $_GET['plugin'] ?? '';

if (empty($token) || empty($pluginId)) {
    http_response_code(400);
    die('Invalid download request.');
}

// Validate the download token
$tokenData = validateDownloadToken($token, $config['store']['downloadExpiry']);
if (!$tokenData || $tokenData['plugin_id'] != $pluginId) {
    http_response_code(403);
    die('Invalid or expired download token.');
}

// Get plugin information
$plugin = getPluginById($config, $pluginId);
if (!$plugin) {
    http_response_code(404);
    die('Plugin not found.');
}

// Check if file exists
$filePath = 'downloads/' . $plugin['file'];
if (!file_exists($filePath)) {
    http_response_code(404);
    die('Plugin file not found.');
}

// Log the download
$logEntry = [
    'timestamp' => date('Y-m-d H:i:s'),
    'plugin_id' => $plugin['id'],
    'plugin_name' => $plugin['name'],
    'user_email' => $tokenData['user_email'],
    'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
];

$logFile = 'logs/downloads.log';
if (!file_exists('logs')) {
    mkdir('logs', 0755, true);
}
file_put_contents($logFile, json_encode($logEntry) . "\n", FILE_APPEND | LOCK_EX);

// Set headers for file download
header('Content-Type: application/octet-stream');
header('Content-Disposition: attachment; filename="' . $plugin['file'] . '"');
header('Content-Length: ' . filesize($filePath));
header('Cache-Control: no-cache, must-revalidate');
header('Expires: 0');

// Output the file
readfile($filePath);
exit;
?>
