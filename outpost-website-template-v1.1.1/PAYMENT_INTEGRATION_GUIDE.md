# Payment Integration Guide

This guide explains how to set up real payment processing for your Rust Plugin Marketplace.

## Overview

The marketplace currently includes placeholder payment processing. To accept real payments, you'll need to integrate with actual payment providers and replace the demo functions.

## Required Dependencies

Install the following packages using Composer:

```bash
composer require paypal/rest-api-sdk-php
composer require stripe/stripe-php
composer require coingate/coingate-php
```

## 1. PayPal Integration

### Setup Steps:

1. **Create PayPal Developer Account**
   - Go to https://developer.paypal.com/
   - Create an application to get Client ID and Secret

2. **Update Configuration**
   ```php
   'paypal' => [
       'enabled' => true,
       'sandbox' => false, // Set to true for testing
       'client_id' => 'YOUR_ACTUAL_PAYPAL_CLIENT_ID',
       'client_secret' => 'YOUR_ACTUAL_PAYPAL_CLIENT_SECRET'
   ]
   ```

3. **Uncomment PayPal Code**
   - In `payment.php`, uncomment the PayPal SDK implementation
   - Remove the demo simulation code

### PayPal Webhook Setup:
- Configure webhooks in PayPal dashboard
- Point to: `https://yourserver.com/paypal-webhook.php`
- Listen for: `PAYMENT.SALE.COMPLETED`

## 2. Stripe Integration

### Setup Steps:

1. **Create Stripe Account**
   - Go to https://stripe.com/
   - Get your API keys from the dashboard

2. **Update Configuration**
   ```php
   'stripe' => [
       'enabled' => true,
       'publishable_key' => 'pk_live_YOUR_ACTUAL_PUBLISHABLE_KEY',
       'secret_key' => 'sk_live_YOUR_ACTUAL_SECRET_KEY'
   ]
   ```

3. **Frontend Integration**
   - Add Stripe.js to your pages
   - Implement Stripe Elements for card input
   - Handle payment confirmation

### Stripe Webhook Setup:
- Configure webhooks in Stripe dashboard
- Point to: `https://yourserver.com/stripe-webhook.php`
- Listen for: `payment_intent.succeeded`

## 3. Cryptocurrency Integration

### Setup Steps:

1. **Choose a Crypto Payment Processor**
   - CoinGate (recommended)
   - BitPay
   - Coinbase Commerce

2. **CoinGate Setup**
   - Create account at https://coingate.com/
   - Get API credentials

3. **Update Configuration**
   ```php
   'crypto' => [
       'enabled' => true,
       'sandbox' => false,
       'api_token' => 'YOUR_COINGATE_API_TOKEN',
       'receive_currency' => 'EUR' // or USD, BTC, etc.
   ]
   ```

## 4. Security Considerations

### SSL Certificate
- **Required**: All payment processing must use HTTPS
- Install SSL certificate on your server
- Redirect all HTTP traffic to HTTPS

### Environment Variables
Store sensitive credentials in environment variables:

```php
// Use environment variables instead of hardcoding
'paypal' => [
    'client_id' => $_ENV['PAYPAL_CLIENT_ID'],
    'client_secret' => $_ENV['PAYPAL_CLIENT_SECRET']
],
'stripe' => [
    'secret_key' => $_ENV['STRIPE_SECRET_KEY']
]
```

### Input Validation
- Validate all payment amounts server-side
- Verify plugin IDs exist
- Sanitize user inputs

## 5. Database Integration

### Recommended Tables:

```sql
-- Orders table
CREATE TABLE orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_steam_id VARCHAR(20) NOT NULL,
    plugin_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) NOT NULL,
    payment_method VARCHAR(20) NOT NULL,
    transaction_id VARCHAR(100) NOT NULL,
    status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Downloads table
CREATE TABLE downloads (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT NOT NULL,
    download_token VARCHAR(64) NOT NULL,
    download_count INT DEFAULT 0,
    max_downloads INT DEFAULT 5,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(id)
);
```

## 6. Email Integration

### Setup SMTP:
```php
// Use PHPMailer or similar
composer require phpmailer/phpmailer

// Configure in config.php
'email' => [
    'smtp_host' => 'smtp.gmail.com',
    'smtp_port' => 587,
    'smtp_username' => '<EMAIL>',
    'smtp_password' => 'your-app-password',
    'from_email' => '<EMAIL>',
    'from_name' => 'Plugin Marketplace'
]
```

## 7. Testing

### Test Mode Setup:
1. Use sandbox/test credentials for all payment providers
2. Test with small amounts (e.g., $0.01)
3. Verify webhook endpoints receive data correctly
4. Test download link generation and expiration

### Test Scenarios:
- Successful payment
- Failed payment
- Cancelled payment
- Webhook failures
- Download link expiration

## 8. Going Live

### Pre-Launch Checklist:
- [ ] SSL certificate installed and working
- [ ] All payment credentials updated to live/production
- [ ] Webhook endpoints tested and working
- [ ] Database backups configured
- [ ] Error logging implemented
- [ ] Email notifications working
- [ ] Download security tested
- [ ] Terms of service and privacy policy added

### Monitoring:
- Set up payment failure alerts
- Monitor webhook delivery
- Track download analytics
- Monitor for suspicious activity

## 9. Legal Considerations

### Required Pages:
- Terms of Service
- Privacy Policy
- Refund Policy
- Contact Information

### Compliance:
- PCI DSS compliance (handled by payment processors)
- GDPR compliance (if serving EU customers)
- Local tax requirements

## Support

For technical support with payment integration:
- PayPal: https://developer.paypal.com/support/
- Stripe: https://support.stripe.com/
- CoinGate: https://support.coingate.com/

Remember to never store credit card information on your servers. Always use the payment processor's secure vaults and tokenization systems.
