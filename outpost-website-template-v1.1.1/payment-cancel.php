<?php
session_start();

/**
 * payment-cancel.php
 * Payment Cancellation Page
 * @owner     Plugin Marketplace
 * @copyright 2024 Plugin Marketplace
 */

require_once __DIR__ . '/plugin-core.php';

// Check if user is logged in
if (!isset($_SESSION['steamid'])) {
    header('Location: index.php');
    exit;
}

// Get plugin details if provided
$pluginId = $_GET['plugin_id'] ?? '';
$plugin = null;
if ($pluginId) {
    $plugin = getPluginById($config, $pluginId);
}

include 'templates/head.php';
include 'templates/navigation.php';
?>

<div class="container">
    <main>
        <section class="payment-cancel py-5">
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="card bg-dark text-white">
                        <div class="card-header bg-warning text-dark">
                            <h3 class="mb-0"><i class="fas fa-exclamation-triangle"></i> Payment Cancelled</h3>
                        </div>
                        <div class="card-body">
                            <h4>Your payment was cancelled</h4>
                            <p class="lead">No charges have been made to your account.</p>
                            
                            <?php if ($plugin): ?>
                                <div class="plugin-info mt-4">
                                    <h5>You were purchasing:</h5>
                                    <div class="d-flex align-items-center">
                                        <img src="assets/img/<?php echo $plugin['image']; ?>" alt="<?php echo htmlspecialchars($plugin['name']); ?>" class="me-3" style="width: 80px; height: 80px; object-fit: cover; border-radius: 8px;">
                                        <div>
                                            <h6 class="mb-1"><?php echo htmlspecialchars($plugin['name']); ?></h6>
                                            <p class="mb-1 text-muted"><?php echo htmlspecialchars($plugin['category']); ?></p>
                                            <p class="mb-0 text-success fw-bold">$<?php echo number_format($plugin['price'], 2); ?></p>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <div class="next-steps mt-4">
                                <h5>What would you like to do next?</h5>
                                <div class="row mt-3">
                                    <div class="col-md-6 mb-3">
                                        <div class="card bg-secondary">
                                            <div class="card-body text-center">
                                                <i class="fas fa-redo fa-2x mb-2"></i>
                                                <h6>Try Again</h6>
                                                <p class="small">Complete your purchase with a different payment method</p>
                                                <?php if ($plugin): ?>
                                                <button class="btn btn-primary btn-sm" onclick="window.location.href='index.php#plugin<?php echo $plugin['id']; ?>'">
                                                    Purchase Now
                                                </button>
                                                <?php else: ?>
                                                <button class="btn btn-primary btn-sm" onclick="window.location.href='index.php#plugins'">
                                                    Browse Plugins
                                                </button>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <div class="card bg-secondary">
                                            <div class="card-body text-center">
                                                <i class="fas fa-question-circle fa-2x mb-2"></i>
                                                <h6>Need Help?</h6>
                                                <p class="small">Contact our support team for assistance</p>
                                                <a href="mailto:<?php echo $config['store']['supportEmail'] ?? '<EMAIL>'; ?>" class="btn btn-outline-light btn-sm">
                                                    Contact Support
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="common-issues mt-4">
                                <h5>Common reasons for payment cancellation:</h5>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-circle text-warning me-2" style="font-size: 0.5rem;"></i> Changed your mind about the purchase</li>
                                    <li><i class="fas fa-circle text-warning me-2" style="font-size: 0.5rem;"></i> Wanted to use a different payment method</li>
                                    <li><i class="fas fa-circle text-warning me-2" style="font-size: 0.5rem;"></i> Encountered technical difficulties</li>
                                    <li><i class="fas fa-circle text-warning me-2" style="font-size: 0.5rem;"></i> Need to verify plugin compatibility</li>
                                </ul>
                            </div>
                            
                            <div class="payment-methods mt-4">
                                <h5>Available Payment Methods:</h5>
                                <div class="d-flex gap-3 mt-2">
                                    <?php if ($config['store']['paymentMethods']['paypal']): ?>
                                    <div class="payment-method">
                                        <i class="fab fa-paypal fa-2x text-primary"></i>
                                        <small class="d-block">PayPal</small>
                                    </div>
                                    <?php endif; ?>
                                    <?php if ($config['store']['paymentMethods']['stripe']): ?>
                                    <div class="payment-method">
                                        <i class="fab fa-cc-stripe fa-2x text-info"></i>
                                        <small class="d-block">Credit Card</small>
                                    </div>
                                    <?php endif; ?>
                                    <?php if ($config['store']['paymentMethods']['crypto']): ?>
                                    <div class="payment-method">
                                        <i class="fab fa-bitcoin fa-2x text-warning"></i>
                                        <small class="d-block">Crypto</small>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer">
                            <div class="d-flex justify-content-between">
                                <a href="index.php" class="btn btn-secondary">
                                    <i class="fas fa-home"></i> Back to Store
                                </a>
                                <div>
                                    <?php if ($plugin): ?>
                                    <a href="index.php#plugin<?php echo $plugin['id']; ?>" class="btn btn-primary me-2">
                                        <i class="fas fa-shopping-cart"></i> Try Purchase Again
                                    </a>
                                    <?php endif; ?>
                                    <a href="index.php#plugins" class="btn btn-outline-light">
                                        <i class="fas fa-search"></i> Browse More Plugins
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>
</div>

<?php include 'templates/footer.php'; ?>

<style>
.payment-cancel {
    min-height: 60vh;
    display: flex;
    align-items: center;
}

.card {
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.card-header.bg-warning {
    background: linear-gradient(135deg, #ffc107, #fd7e14) !important;
}

.payment-method {
    text-align: center;
    padding: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.05);
}

.plugin-info img {
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.common-issues ul li {
    padding: 0.25rem 0;
}

.next-steps .card {
    transition: transform 0.2s ease;
}

.next-steps .card:hover {
    transform: translateY(-2px);
}
</style>
