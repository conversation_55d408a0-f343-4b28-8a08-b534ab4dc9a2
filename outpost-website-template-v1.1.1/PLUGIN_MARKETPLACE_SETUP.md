# Rust Plugin Marketplace Setup Guide

This guide will help you set up your Rust plugin marketplace website on Ubuntu with your custom domain.

## Prerequisites

- Ubuntu server (18.04 or later)
- Domain name pointed to your server
- Basic knowledge of Linux commands

## Step 1: Install Required Software

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Apache, PHP, and required modules
sudo apt install apache2 php php-mysql php-curl php-json php-mbstring php-xml php-zip -y

# Enable Apache modules
sudo a2enmod rewrite
sudo a2enmod ssl

# Start and enable Apache
sudo systemctl start apache2
sudo systemctl enable apache2
```

## Step 2: Configure Domain and SSL

```bash
# Install Certbot for SSL certificates
sudo apt install certbot python3-certbot-apache -y

# Get SSL certificate for your domain
sudo certbot --apache -d yourdomain.com -d www.yourdomain.com

# The certificate will auto-renew
```

## Step 3: Upload Website Files

1. Upload all website files to `/var/www/html/` or your domain's document root
2. Set proper permissions:

```bash
sudo chown -R www-data:www-data /var/www/html/
sudo chmod -R 755 /var/www/html/
sudo chmod -R 777 /var/www/html/cache/
sudo chmod -R 777 /var/www/html/logs/
sudo chmod -R 777 /var/www/html/downloads/
```

## Step 4: Configure the Website

1. **Steam API Setup:**
   - Get your Steam API key from: https://steamcommunity.com/dev/apikey
   - Edit `steamauth/steamConfig.php` and add your API key

2. **Admin Configuration:**
   - Edit `admin/config.php`
   - Add your Steam ID to the admin array
   - Get your Steam ID from: https://steamidfinder.com/

3. **Website Configuration:**
   - Edit `config.php` to customize your site settings
   - Update the title, description, and other settings
   - Configure payment methods in the store section

## Step 5: Add Your Plugins

1. **Upload Plugin Files:**
   - Place your `.cs` plugin files in the `downloads/` directory
   - Upload plugin images to `assets/img/` directory

2. **Add Plugins via Admin Panel:**
   - Log in with Steam
   - Go to the Web Panel (link in footer)
   - Click "Plugin Management"
   - Add your plugins with details, pricing, and images

## Step 6: Payment Integration

### PayPal Integration
1. Create a PayPal developer account
2. Get your API credentials
3. Update `payment.php` with PayPal SDK integration

### Stripe Integration
1. Create a Stripe account
2. Get your API keys
3. Update `payment.php` with Stripe SDK integration

## Step 7: Security Considerations

1. **File Permissions:**
   ```bash
   # Secure sensitive files
   sudo chmod 600 config.php
   sudo chmod 600 steamauth/steamConfig.php
   sudo chmod 600 admin/config.php
   ```

2. **Apache Configuration:**
   Create `.htaccess` in root directory:
   ```apache
   # Protect sensitive directories
   <Files "config.php">
       Order allow,deny
       Deny from all
   </Files>
   
   <Directory "admin/">
       AuthType Basic
       AuthName "Admin Area"
       Require valid-user
   </Directory>
   ```

## Step 8: Testing

1. Visit your domain
2. Test Steam login functionality
3. Test plugin browsing and search
4. Test admin panel access
5. Test payment flow (use test mode first)

## Customization Tips

### Adding New Plugin Categories
Edit `config.php` and add to the `pluginCategories` array:
```php
'pluginCategories' => [
    'Admin Tools',
    'PvP',
    'PvE',
    'Economy',
    'Building',
    'Transportation',
    'Chat & Communication',
    'Events',
    'Utilities',
    'Fun & Games',
    'Your New Category'  // Add here
],
```

### Customizing Appearance
- Edit `assets/css/plugins.css` for plugin-specific styles
- Edit `assets/css/style.css` for general website styles
- Replace images in `assets/img/` directory

### Adding New Payment Methods
1. Update the payment methods in `config.php`
2. Add the payment processing logic in `payment.php`
3. Update the frontend JavaScript in `index.php`

## Maintenance

### Regular Tasks
- Monitor `logs/` directory for purchase and download logs
- Update plugin files when new versions are available
- Backup your configuration files regularly
- Monitor SSL certificate renewal

### Updating Plugins
1. Upload new plugin file to `downloads/` directory
2. Update plugin information via admin panel
3. Update version number and changelog

## Troubleshooting

### Common Issues
1. **Steam login not working:** Check Steam API key and domain configuration
2. **Admin panel access denied:** Verify Steam ID in admin config
3. **File download issues:** Check file permissions and paths
4. **Payment processing errors:** Verify API credentials and test mode settings

### Log Files
- Apache error log: `/var/log/apache2/error.log`
- Purchase log: `logs/purchases.log`
- Download log: `logs/downloads.log`

## Support

For additional help:
1. Check the original template documentation
2. Review PHP error logs
3. Test in a development environment first
4. Consider hiring a developer for complex customizations

## Security Notes

- Always use HTTPS for payment processing
- Regularly update PHP and Apache
- Use strong passwords for admin accounts
- Monitor access logs for suspicious activity
- Consider implementing rate limiting for API endpoints
