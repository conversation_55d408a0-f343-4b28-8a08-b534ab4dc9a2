# Plugin Marketplace Security Configuration

# Enable URL rewriting
RewriteEngine On

# Protect sensitive configuration files
<Files "config.php">
    Order allow,deny
    Deny from all
</Files>

<Files "plugin-core.php">
    Order allow,deny
    Deny from all
</Files>

<Files "core.php">
    Order allow,deny
    Deny from all
</Files>

# Protect Steam authentication files
<FilesMatch "steamauth/.*\.php$">
    <RequireAll>
        Require all denied
        Require local
    </RequireAll>
</FilesMatch>

# Protect admin configuration
<Files "admin/config.php">
    Order allow,deny
    Deny from all
</Files>

# Protect log files
<FilesMatch "^logs/.*">
    Order allow,deny
    Deny from all
</FilesMatch>

# Protect cache files from direct access
<FilesMatch "^cache/.*">
    Order allow,deny
    Deny from all
</FilesMatch>

# Protect download directory (only allow access through download.php)
<FilesMatch "^downloads/.*">
    Order allow,deny
    Deny from all
</FilesMatch>

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# Prevent access to hidden files
<FilesMatch "^\.">
    Order allow,deny
    Deny from all
</FilesMatch>

# Prevent access to backup files
<FilesMatch "\.(bak|backup|old|tmp)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Enable compression for better performance
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Cache static assets
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
</IfModule>

# PHP Configuration Settings
php_value upload_max_filesize 50M
php_value post_max_size 50M
php_value max_execution_time 300
php_value memory_limit 256M
