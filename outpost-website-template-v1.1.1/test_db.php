<?php
// Test database connection
require_once 'database.php';

try {
    $pdo = new PDO(
        "mysql:host={$db_config['host']};dbname={$db_config['database']};charset={$db_config['charset']}",
        $db_config['username'],
        $db_config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ]
    );
    
    echo "<h2>Database Connection Test</h2>";
    echo "<p style='color: green;'>✓ Database connection successful!</p>";
    
    // Test table existence
    $tables = ['users', 'orders', 'licenses'];
    echo "<h3>Table Check:</h3>";
    foreach ($tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "<p style='color: green;'>✓ Table '$table' exists</p>";
        } else {
            echo "<p style='color: red;'>✗ Table '$table' missing</p>";
        }
    }
    
    // Test PHP info
    echo "<h3>PHP Configuration:</h3>";
    echo "<p>PHP Version: " . phpversion() . "</p>";
    echo "<p>MySQL Extension: " . (extension_loaded('pdo_mysql') ? '✓ Loaded' : '✗ Not loaded') . "</p>";
    echo "<p>Upload Max Filesize: " . ini_get('upload_max_filesize') . "</p>";
    echo "<p>Post Max Size: " . ini_get('post_max_size') . "</p>";
    echo "<p>Max Execution Time: " . ini_get('max_execution_time') . "</p>";
    echo "<p>Memory Limit: " . ini_get('memory_limit') . "</p>";
    
} catch (PDOException $e) {
    echo "<h2>Database Connection Test</h2>";
    echo "<p style='color: red;'>✗ Database connection failed: " . $e->getMessage() . "</p>";
}
?>
