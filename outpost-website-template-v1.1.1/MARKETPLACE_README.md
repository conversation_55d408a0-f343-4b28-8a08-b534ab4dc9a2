# Rust Plugin Marketplace

A complete e-commerce solution for selling Rust server plugins, converted from the original Outpost website template.

## 🚀 Features

### Core Functionality
- **Steam Authentication**: Secure login using Steam accounts
- **Plugin Marketplace**: Browse, search, and filter plugins by category
- **Multiple Payment Methods**: PayPal, Stripe, and cryptocurrency support
- **Secure Downloads**: License key generation and download tracking
- **License Management**: Complete license tracking and user management
- **Responsive Design**: Mobile-friendly Bootstrap interface

### Advanced Features
- **Category System**: Organize plugins (PvP, PvE, Admin Tools, Economy, Fun, Utility)
- **Search & Filtering**: Advanced search with multiple sorting options
- **Featured Plugins**: Highlight popular or new plugins
- **Download Limits**: Configurable download limits per license
- **Security**: Rate limiting, file protection, and secure token system
- **Logging**: Comprehensive logging for sales, downloads, and user activity

## 📋 Requirements

### Server Requirements
- **OS**: Ubuntu 20.04 LTS or newer
- **Web Server**: Nginx
- **PHP**: 8.1+ with extensions (fpm, mysql, curl, gd, mbstring, xml, zip, json, opcache)
- **Database**: MySQL 8.0+ (optional but recommended)
- **SSL Certificate**: Required for payment processing

### Payment Provider Accounts
- **PayPal**: Business account with API access
- **Stripe**: Account with API keys
- **CoinGate**: Account for cryptocurrency payments (optional)

## 🛠️ Quick Start

### Option 1: Automated Setup (Recommended)
```bash
# 1. Upload files to your server
scp -r * user@server:/var/www/your-domain.com/

# 2. Run automated setup script
sudo ./setup-domain.sh your-domain.com

# 3. Configure database (follow prompts in UBUNTU_DEPLOYMENT_GUIDE.md)

# 4. Update configuration
nano /var/www/your-domain.com/config.php

# 5. Setup SSL after DNS is configured
/var/www/your-domain.com/setup-ssl.sh
```

### Option 2: Manual Installation
Follow the comprehensive guide in `UBUNTU_DEPLOYMENT_GUIDE.md`

## 📁 Key Files

```
/
├── index.php                    # Main marketplace page
├── config.php                   # Main configuration file
├── plugin-core.php              # Core plugin functions
├── payment.php                  # Payment processing
├── download-enhanced.php        # Enhanced download with license keys
├── license-manager.php          # License management interface
├── payment-success.php          # Payment success page
├── payment-cancel.php           # Payment cancellation page
├── setup-domain.sh              # Automated setup script
├── templates/                   # HTML templates
├── assets/                      # Static assets
├── downloads/                   # Plugin files (secure)
├── logs/                        # Application logs
└── docs/                        # Documentation
```

## ⚙️ Configuration

### Basic Configuration
Edit `config.php` to customize your marketplace:

```php
// Site Information
'title' => 'Your Server Name',
'description' => 'Your server description',
'domain' => 'https://your-domain.com',

// Payment Configuration
'store' => [
    'enabled' => 'yes',
    'currency' => 'USD',
    'return_url' => 'https://your-domain.com/payment-success.php',
    'cancel_url' => 'https://your-domain.com/payment-cancel.php',
    
    // PayPal Settings
    'paypal' => [
        'enabled' => true,
        'sandbox' => false, // Set to true for testing
        'client_id' => 'YOUR_PAYPAL_CLIENT_ID',
        'client_secret' => 'YOUR_PAYPAL_CLIENT_SECRET'
    ],
    
    // Stripe Settings
    'stripe' => [
        'enabled' => true,
        'publishable_key' => 'pk_live_YOUR_STRIPE_PUBLISHABLE_KEY',
        'secret_key' => 'sk_live_YOUR_STRIPE_SECRET_KEY'
    ]
]
```

### Adding Plugins
Add plugins to the `plugins` array in `config.php`:

```php
'plugins' => [
    [
        'id' => 1,
        'name' => 'Advanced Teleportation',
        'description' => 'Complete teleportation system with cooldowns...',
        'short_description' => 'Advanced teleportation with cooldowns',
        'price' => 15.00,
        'currency' => 'USD',
        'image' => 'plugin-teleport.jpg',
        'file' => 'AdvancedTeleportation.cs',
        'version' => '2.1.0',
        'author' => 'PluginDev',
        'category' => 'Admin Tools',
        'featured' => true,
        'enabled' => true,
        'date_added' => '2024-06-24',
        'requirements' => [
            'oxide_version' => '2.0.0',
            'rust_version' => 'Any',
            'dependencies' => []
        ],
        'features' => [
            'Multiple teleport locations',
            'Cooldown system',
            'Permission-based access',
            'Admin override commands'
        ],
        'changelog' => [
            '2.1.0' => 'Added new teleport effects and bug fixes',
            '2.0.0' => 'Major rewrite with new features'
        ]
    ]
]
```

## 💳 Payment Integration

### Supported Payment Methods
- **PayPal**: Most popular, easy setup, worldwide support
- **Stripe**: Credit/debit cards, extensive features, global coverage
- **Cryptocurrency**: Bitcoin, Ethereum, and more via CoinGate

### Setup Instructions
1. **PayPal**: Get Client ID and Secret from PayPal Developer Console
2. **Stripe**: Get API keys from Stripe Dashboard
3. **Crypto**: Sign up at CoinGate for cryptocurrency support

Detailed setup instructions in `PAYMENT_INTEGRATION_GUIDE.md`

## 🔐 Security Features

### Built-in Security
- **Rate Limiting**: Prevents abuse and spam attacks
- **File Protection**: Sensitive files and directories protected
- **Secure Downloads**: Token-based download system with expiration
- **Input Validation**: All user inputs validated and sanitized
- **SQL Injection Protection**: Prepared statements used throughout
- **XSS Protection**: Output properly escaped and filtered

### Security Best Practices
- Keep all software updated
- Use strong, unique passwords
- Enable fail2ban for intrusion prevention
- Regular security audits and monitoring
- Monitor logs for suspicious activity

## 📚 Documentation

### For Administrators
- **`ADMIN_GUIDE.md`**: Complete marketplace management guide
- **`UBUNTU_DEPLOYMENT_GUIDE.md`**: Detailed server setup and configuration
- **`PAYMENT_INTEGRATION_GUIDE.md`**: Payment provider setup and configuration
- **`QUICK_START_GUIDE.md`**: Fast deployment instructions

### For Users
- **`USER_GUIDE.md`**: Customer guide for using the marketplace

### For Developers
- **Inline Documentation**: Extensive code comments and function documentation
- **API Documentation**: Payment and download API documentation
- **Configuration Examples**: Sample configurations for various setups

## 🛠️ Management

### Daily Tasks
- Monitor system status and performance
- Check application and server logs
- Review sales and download activity
- Respond to customer support requests

### Weekly Tasks
- Create system backups
- Update system packages
- Review security logs and failed attempts
- Clean old log files

### Monthly Tasks
- Review SSL certificate status
- Check payment provider statements
- Update plugins and add new content
- Performance optimization review

### Useful Commands
```bash
# Monitor system status
/home/<USER>/monitor-your-domain.sh

# Create backup
/home/<USER>/backup-your-domain.sh

# Check logs
tail -f /var/www/your-domain.com/logs/*.log

# Restart services
sudo systemctl restart nginx php8.1-fpm

# Check SSL status
sudo certbot certificates
```

## 🚨 Troubleshooting

### Common Issues

1. **500 Internal Server Error**
   ```bash
   # Check PHP and Nginx error logs
   tail -f /var/log/php8.1-fpm.log
   tail -f /var/log/nginx/error.log
   
   # Fix permissions
   sudo chown -R www-data:www-data /var/www/your-domain.com
   ```

2. **Payment Issues**
   - Verify API credentials in config.php
   - Check sandbox vs production mode
   - Review payment provider dashboards

3. **Download Problems**
   - Check file permissions in downloads directory
   - Verify token generation in logs
   - Ensure download limits not exceeded

4. **Performance Issues**
   - Enable OPcache for PHP
   - Configure Nginx caching
   - Monitor server resources

### Getting Help
1. **Check Documentation**: Review relevant guide files first
2. **Check Logs**: Application and server logs contain valuable information
3. **Test Environment**: Set up a test environment to debug issues
4. **Community**: Ask for help in Rust server admin communities

## 🔄 Updates and Maintenance

### Update Process
1. **Backup**: Always create full backup before updates
2. **Test**: Test updates in development environment first
3. **Deploy**: Apply updates to production carefully
4. **Verify**: Confirm all functionality works correctly

### Maintenance Schedule
- **Daily**: System monitoring and log review
- **Weekly**: Backups and security updates
- **Monthly**: Performance review and optimization
- **Quarterly**: Full security audit and dependency updates

## 📄 License and Legal

### Usage Rights
- This marketplace system is provided for educational and commercial use
- Ensure compliance with payment provider terms of service
- Follow Steam API terms of use for authentication
- Comply with local laws and regulations
- Respect plugin licensing agreements

### Disclaimer
- Use at your own risk
- No warranty provided
- Test thoroughly before production use
- Keep regular backups

## 🎯 Going Live Checklist

Before making your marketplace public:

- [ ] All payment methods tested in production mode
- [ ] SSL certificate installed and working properly
- [ ] All configuration values updated from defaults
- [ ] Backup system configured and tested
- [ ] Monitoring and alerting systems set up
- [ ] Terms of service and privacy policy added
- [ ] Contact information and support system ready
- [ ] Plugin files uploaded and tested
- [ ] Admin access configured and secured
- [ ] Security hardening completed
- [ ] Performance optimization applied

## 🚀 Next Steps

After your marketplace is live:

1. **Content**: Regularly add new plugins to keep content fresh
2. **Marketing**: Promote your marketplace in Rust communities
3. **Analytics**: Monitor performance with tools like Google Analytics
4. **Support**: Set up customer support system
5. **Feedback**: Collect and implement user feedback
6. **Growth**: Expand features based on user needs

---

**Ready to launch your Rust plugin marketplace?** Start with the `QUICK_START_GUIDE.md`! 🚀
