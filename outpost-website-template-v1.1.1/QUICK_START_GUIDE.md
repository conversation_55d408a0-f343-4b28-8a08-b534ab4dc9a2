# Quick Start Guide - Rust Plugin Marketplace

This guide will help you quickly deploy your plugin marketplace on Ubuntu with your custom domain.

## 🚀 Quick Deployment Steps

### 1. Prepare Your Ubuntu Server

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install required packages
sudo apt install -y nginx php8.1-fpm php8.1-mysql php8.1-curl php8.1-gd php8.1-mbstring php8.1-xml php8.1-zip php8.1-json php8.1-opcache mysql-server certbot python3-certbot-nginx git composer curl wget unzip
```

### 2. Upload Files to Server

Upload all your marketplace files to your server:

```bash
# Create directory
sudo mkdir -p /var/www/your-domain.com

# Upload files (use SCP, SFTP, or git)
# Example with git:
cd /var/www/your-domain.com
sudo git clone https://github.com/yourusername/your-repo.git .

# Or upload via SCP from your local machine:
# scp -r * user@your-server:/var/www/your-domain.com/
```

### 3. Run Domain Setup Script

```bash
# Make script executable
chmod +x setup-domain.sh

# Run setup script (replace with your actual domain)
sudo ./setup-domain.sh your-domain.com
```

### 4. Configure Database

```bash
# Secure MySQL
sudo mysql_secure_installation

# Create database
sudo mysql -u root -p
```

```sql
CREATE DATABASE plugin_marketplace;
CREATE USER 'marketplace_user'@'localhost' IDENTIFIED BY 'your_secure_password';
GRANT ALL PRIVILEGES ON plugin_marketplace.* TO 'marketplace_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

Run the SQL from `UBUNTU_DEPLOYMENT_GUIDE.md` to create tables.

### 5. Configure Application

```bash
# Copy environment template
cp /var/www/your-domain.com/.env.example /var/www/your-domain.com/.env

# Edit configuration
nano /var/www/your-domain.com/.env
```

Update with your actual values:
- Database credentials
- Payment provider API keys
- Domain name
- Email settings

```bash
# Copy database template
cp /var/www/your-domain.com/database-config-template.php /var/www/your-domain.com/database.php

# Edit database config
nano /var/www/your-domain.com/database.php
```

### 6. Point Domain to Server

Configure DNS with your domain provider:
```
A     @              YOUR_SERVER_IP
A     www            YOUR_SERVER_IP
```

### 7. Setup SSL Certificate

```bash
# Run SSL setup (after DNS is configured)
/var/www/your-domain.com/setup-ssl.sh
```

### 8. Test Your Site

Visit `https://your-domain.com` to test your marketplace!

## 🔧 Configuration Checklist

### Required Configurations

- [ ] **Database**: Update `database.php` with your MySQL credentials
- [ ] **Domain**: Update `config.php` with your domain name
- [ ] **PayPal**: Add your PayPal Client ID and Secret
- [ ] **Stripe**: Add your Stripe API keys
- [ ] **Email**: Configure SMTP settings for notifications
- [ ] **Steam API**: Ensure Steam authentication is working

### Payment Provider Setup

#### PayPal
1. Go to [PayPal Developer](https://developer.paypal.com/)
2. Create an application
3. Get Client ID and Client Secret
4. Update in `config.php`

#### Stripe
1. Go to [Stripe Dashboard](https://dashboard.stripe.com/)
2. Get API keys from Developers > API keys
3. Update in `config.php`

#### Cryptocurrency (Optional)
1. Sign up at [CoinGate](https://coingate.com/)
2. Get API credentials
3. Update in `config.php`

### Security Checklist

- [ ] **Firewall**: Configure UFW firewall
- [ ] **Fail2Ban**: Install and configure fail2ban
- [ ] **SSL**: Install SSL certificate
- [ ] **Backups**: Set up automated backups
- [ ] **Updates**: Configure automatic security updates

## 📁 File Structure

```
/var/www/your-domain.com/
├── index.php                 # Main page
├── config.php               # Main configuration
├── database.php             # Database connection
├── .env                     # Environment variables
├── plugin-core.php          # Core plugin functions
├── payment.php              # Payment processing
├── download.php             # File downloads
├── download-enhanced.php    # Enhanced download system
├── license-manager.php      # License management
├── payment-success.php      # Payment success page
├── payment-cancel.php       # Payment cancel page
├── templates/               # HTML templates
├── assets/                  # CSS, JS, images
├── steamauth/              # Steam authentication
├── downloads/              # Plugin files (secure)
├── logs/                   # Application logs
└── admin/                  # Admin panel (if exists)
```

## 🔍 Testing Your Setup

### 1. Basic Functionality
- [ ] Site loads correctly
- [ ] Steam login works
- [ ] Plugin browsing works
- [ ] Search and filtering work

### 2. Payment Testing
- [ ] PayPal sandbox payments work
- [ ] Stripe test payments work
- [ ] Payment success/cancel pages work
- [ ] Download tokens are generated

### 3. Download System
- [ ] Download links work
- [ ] License keys are generated
- [ ] Download limits are enforced
- [ ] Files are properly secured

### 4. Security Testing
- [ ] Sensitive files are protected
- [ ] SQL injection protection works
- [ ] XSS protection is active
- [ ] Rate limiting is working

## 🛠️ Maintenance Commands

```bash
# Monitor system
/home/<USER>/monitor-your-domain.sh

# Create backup
/home/<USER>/backup-your-domain.sh

# Check logs
tail -f /var/www/your-domain.com/logs/downloads.log
tail -f /var/www/your-domain.com/logs/purchases.log
tail -f /var/log/nginx/error.log

# Restart services
sudo systemctl restart nginx
sudo systemctl restart php8.1-fpm
sudo systemctl restart mysql

# Update SSL certificate
sudo certbot renew

# Check SSL status
sudo certbot certificates
```

## 🚨 Troubleshooting

### Common Issues

1. **500 Internal Server Error**
   ```bash
   # Check PHP errors
   tail -f /var/log/php8.1-fpm.log
   
   # Check Nginx errors
   tail -f /var/log/nginx/error.log
   
   # Fix permissions
   sudo chown -R www-data:www-data /var/www/your-domain.com
   ```

2. **Database Connection Failed**
   ```bash
   # Test database connection
   mysql -u marketplace_user -p plugin_marketplace
   
   # Check database.php configuration
   ```

3. **Steam Login Not Working**
   - Verify Steam API key in config.php
   - Check steamauth configuration
   - Ensure HTTPS is working

4. **Payment Issues**
   - Verify API credentials
   - Check sandbox/production mode
   - Review payment logs

### Performance Issues

1. **Slow Loading**
   ```bash
   # Enable OPcache
   sudo nano /etc/php/8.1/fpm/conf.d/10-opcache.ini
   
   # Add Nginx caching
   # Check UBUNTU_DEPLOYMENT_GUIDE.md for details
   ```

2. **High Memory Usage**
   ```bash
   # Monitor resources
   htop
   
   # Check PHP memory limit
   php -i | grep memory_limit
   ```

## 📞 Support

If you need help:

1. **Check Logs**: Always check application and server logs first
2. **Documentation**: Review the full `UBUNTU_DEPLOYMENT_GUIDE.md`
3. **Test Environment**: Set up a test environment to debug issues
4. **Community**: Ask for help in Rust server communities

## 🎯 Going Live Checklist

Before making your marketplace public:

- [ ] All payments tested in production mode
- [ ] SSL certificate installed and working
- [ ] All configuration values updated from defaults
- [ ] Backup system configured and tested
- [ ] Monitoring and alerting set up
- [ ] Terms of service and privacy policy added
- [ ] Contact information updated
- [ ] Plugin files uploaded and tested
- [ ] Admin access configured
- [ ] Security hardening completed

## 📈 Next Steps

After your marketplace is live:

1. **Add More Plugins**: Regularly add new plugins to keep content fresh
2. **Monitor Performance**: Use tools like Google Analytics
3. **Customer Support**: Set up a support system for customers
4. **Marketing**: Promote your marketplace in Rust communities
5. **Updates**: Keep your system and plugins updated
6. **Feedback**: Collect and implement user feedback

Your Rust Plugin Marketplace is now ready to serve your community! 🎉
